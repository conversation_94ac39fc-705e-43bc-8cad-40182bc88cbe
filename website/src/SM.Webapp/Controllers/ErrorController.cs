using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using SM.Service.Contents.Contracts;
using SM.Webapp.Core;
using SM.Webapp.Resources;
using System.Net;

namespace SM.Webapp.Controllers;

[Route("Error")]
public class ErrorController : WebController
{
    public ErrorController(ILogger<ErrorController> logger, IContentService contentService) : base(contentService,
        logger)
    {
    }

    [Route("")]
    public async Task<IActionResult> Index()
    {
        var exceptionData = HttpContext.Features.Get<IExceptionHandlerPathFeature>();

        _logger.LogError("Exception Url: {url}, Exception Message: {message}",
            exceptionData?.Path, exceptionData?.Error?.Message);

        var languages = await _contentService.GetLanguages(RouteData.GetLanguage());
        ViewBag.Languages = languages.prepareLanguageDropdown();
        ViewBag.StatusCode = (int)HttpStatusCode.InternalServerError;
        ViewBag.ErrorMessage = LocalizerRes.error_message_500;
        HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

        return View("Error");
    }

    [Route("{statusCode:int}")]
    public async Task<IActionResult> HandleErrorCode(int statusCode)
    {
        var statusCodeData = HttpContext.Features.Get<IStatusCodeReExecuteFeature>();

        _logger.LogError("Status Code:{statusCode}, Request Path: {path}",
            statusCode, statusCodeData?.OriginalPath);

        var languages = await _contentService.GetLanguages(RouteData.GetLanguage());
        ViewBag.Languages = languages.prepareLanguageDropdown();
        ViewBag.StatusCode = statusCode;

        ViewBag.ErrorMessage = statusCode switch
        {
            404 => LocalizerRes.error_message_404,
            500 => LocalizerRes.error_message_500,
            _ => LocalizerRes.error_message_500
        };

        HttpContext.Response.StatusCode = statusCode;
        return View("Error");
    }

    [Route("404")]
    public async Task<IActionResult> NotFound()
    {
        var languages = await _contentService.GetLanguages(RouteData.GetLanguage());
        ViewBag.Languages = languages.prepareLanguageDropdown();
        ViewBag.StatusCode = 404;
        ViewBag.ErrorMessage = LocalizerRes.error_message_404;
        HttpContext.Response.StatusCode = 404;

        return View("Error");
    }
}