using System.Net;
using Microsoft.AspNetCore.Mvc;
using SM.Service.Catalog.Exceptions;
using SM.Service.Contents.Contracts;
using SM.Service.Contents.DTOs;
using SM.Service.Contents.Implementations;
using SM.Webapp.Resources;

namespace SM.Webapp.Core;

//[ServiceFilter(typeof(ExceptionFilter))]
//[ServiceFilter(typeof(GlobalActionFilter))]
public class WebController : Controller
{
    protected readonly IContentService _contentService;
    protected ILogger<WebController> _logger;

    public WebController(IContentService contentService, ILogger<WebController> logger)
    {
        _contentService = contentService;
        _logger = logger;
    }

    protected string CurrentCultureInfo => RouteData.GetLanguage();
    protected string Slug => RouteData.GetSlug();

    protected string PageTitle(string title)
    {
        return title + " | "+ LocalizerRes.page_meta_title_brand;
    }

    protected string PageDescription(string description="")
    {
        return string.IsNullOrEmpty(description) ? LocalizerRes.seo_page_main_description: description;
    }
    protected string PageKeywords(string keywords="")
    {
        return string.IsNullOrEmpty(keywords) ? LocalizerRes.seo_page_main_keywords:keywords;
    }

    // Open Graph Meta Tags Helper Methods
    protected string OgTitle(string title = "")
    {
        return string.IsNullOrEmpty(title) ? LocalizerRes.seo_page_main_title : title;
    }

    protected string OgDescription(string description = "")
    {
        return string.IsNullOrEmpty(description) ? LocalizerRes.seo_page_main_description : description;
    }

    protected string OgImage(string imagePath = "")
    {
        var baseUrl = "https://www.sabunmutfagi.com";
        return string.IsNullOrEmpty(imagePath) ?
            $"{baseUrl}/images/logos/sabun-mutfagi-og-image.jpg" :
            $"{baseUrl}{imagePath}";
    }

    protected string OgUrl()
    {
        return $"https://www.sabunmutfagi.com{HttpContext.Request.Path}";
    }

    protected string OgType(string type = "website")
    {
        return type;
    }

    protected string OgSiteName()
    {
        return LocalizerRes.page_meta_title_brand;
    }

    // Twitter Cards Helper Methods
    protected string TwitterCard(string cardType = "summary_large_image")
    {
        return cardType;
    }

    protected string TwitterSite()
    {
        return "@sabunmutfagi"; // Twitter handle
    }

    protected string TwitterCreator()
    {
        return "@sabunmutfagi"; // Twitter handle
    }

    protected string TwitterTitle(string title = "")
    {
        return string.IsNullOrEmpty(title) ? LocalizerRes.seo_page_main_title : title;
    }

    protected string TwitterDescription(string description = "")
    {
        return string.IsNullOrEmpty(description) ? LocalizerRes.seo_page_main_description : description;
    }

    protected string TwitterImage(string imagePath = "")
    {
        var baseUrl = "https://www.sabunmutfagi.com";
        return string.IsNullOrEmpty(imagePath) ?
            $"{baseUrl}/images/logos/sabun-mutfagi-twitter-card.jpg" :
            $"{baseUrl}{imagePath}";
    }

    // Hreflang Helper Methods
    protected string GetDefaultHreflangUrl()
    {
        return "https://www.sabunmutfagi.com/tr";
    }

    protected string GetXDefaultHreflangUrl()
    {
        return "https://www.sabunmutfagi.com/";
    }

    protected IEnumerable<LanguageDto> PrepareHreflangUrls(IEnumerable<LanguageDto> languages)
    {
        return languages?.Select(lang => new LanguageDto
        {
            CultureInfo = lang.CultureInfo,
            Prefix = lang.Prefix,
            Name = lang.Name,
            ImagePath = lang.ImagePath,
            IsSelected = lang.IsSelected,
            Url = lang.Url
        }) ?? new List<LanguageDto>();
    }

    [NonAction]
    protected ViewResult ReturnView<T>(Func<T> func, string viewName)
    {
        try
        {
            var funcResult = func();

            return View(viewName, funcResult);
        }
        catch (Exception ex)
        {
            return View(viewName);
        }
    }

    [NonAction]
    protected async Task<ViewResult> ReturnViewAsync<T>(Func<Task<T>> func, string viewName)
    {
        try
        {
            var languages = await _contentService.GetLanguages(RouteData.GetLanguage());
            ViewBag.Languages = languages.prepareLanguageDropdown();
            ViewBag.PageTitle = PageTitle(LocalizerRes.seo_page_main_title);
            ViewBag.PageDescription = PageDescription(LocalizerRes.seo_page_main_description);
            ViewBag.PageKeywords = PageKeywords(LocalizerRes.seo_page_main_keywords);
            ViewBag.SocialMediaAccountAddresses = await _contentService.GetSocialMediaAccountAddresses();
            ViewBag.RelCanonicals = languages;

            // Social Media Meta Tags - Default values (can be overridden in specific controllers)
            ViewBag.OgTitle = OgTitle(LocalizerRes.seo_page_main_title);
            ViewBag.OgDescription = OgDescription(LocalizerRes.seo_page_main_description);
            ViewBag.OgImage = OgImage();
            ViewBag.OgUrl = OgUrl();
            ViewBag.OgType = OgType("website");
            ViewBag.TwitterCard = TwitterCard("summary_large_image");
            ViewBag.TwitterTitle = TwitterTitle(LocalizerRes.seo_page_main_title);
            ViewBag.TwitterDescription = TwitterDescription(LocalizerRes.seo_page_main_description);
            ViewBag.TwitterImage = TwitterImage();

            // Hreflang URLs
            ViewBag.HreflangUrls = PrepareHreflangUrls(languages);

            var funcResult = await func();

            return View(viewName, funcResult);
        }
        catch (Exception ex)
        {
            if (ex.InnerException is NotFoundProductException)
            {
                ViewBag.StatusCode = (int)HttpStatusCode.NotFound;
                ViewBag.ErrorMessage = LocalizerRes.error_message_404;
                HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                _logger.LogError("Exception Url: {url}, Exception Message: {message}",
                    HttpContext.Request.Path, ex.Message);
                return View("Error");
            }

            if (ex.InnerException is NotFoundCategoryException)
            {
                ViewBag.StatusCode = (int)HttpStatusCode.NotFound;
                ViewBag.ErrorMessage = LocalizerRes.error_message_404;
                HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                _logger.LogError("Exception Url: {url}, Exception Message: {message}",
                    HttpContext.Request.Path, ex.Message);
                return View("Error");
            }
            
            ViewBag.StatusCode = (int)HttpStatusCode.InternalServerError;
            ViewBag.ErrorMessage = LocalizerRes.error_message_500;
            HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            _logger.LogError("Exception Url: {url}, Exception Message: {message}",
                HttpContext.Request.Path, ex.Message);
            return View("Error");
        }
    }
}