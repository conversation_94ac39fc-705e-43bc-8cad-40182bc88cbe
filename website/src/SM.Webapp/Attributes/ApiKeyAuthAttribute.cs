using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace SM.Webapp.Attributes;

/// <summary>
/// Header tabanlı API key authentication attribute
/// Usage: [ApiKeyAuth]
/// Header: X-API-Key: your-secret-key
/// </summary>
public class ApiKeyAuthAttribute : Attribute, IAuthorizationFilter
{
    private const string API_KEY_HEADER_NAME = "X-API-Key";
    
    public void OnAuthorization(AuthorizationFilterContext context)
    {
        var configuration = context.HttpContext.RequestServices.GetRequiredService<IConfiguration>();
        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<ApiKeyAuthAttribute>>();
        
        // Get expected API key from configuration
        var expectedApiKey = configuration["ApiSettings:SitemapApiKey"];
        
        if (string.IsNullOrEmpty(expectedApiKey))
        {
            logger.LogError("SitemapApiKey not configured in appsettings");
            context.Result = new UnauthorizedObjectResult(new { 
                error = "API key authentication not configured",
                message = "Contact administrator" 
            });
            return;
        }
        
        // Check if API key header exists
        if (!context.HttpContext.Request.Headers.TryGetValue(API_KEY_HEADER_NAME, out var extractedApiKey))
        {
            logger.LogWarning("API key missing in request from {RemoteIpAddress}", 
                context.HttpContext.Connection.RemoteIpAddress);
            
            context.Result = new UnauthorizedObjectResult(new { 
                error = "API key required",
                message = $"Contact administrator" 
            });
            return;
        }
        
        // Validate API key
        if (!expectedApiKey.Equals(extractedApiKey))
        {
            logger.LogWarning("Invalid API key attempt from {RemoteIpAddress}: {ProvidedKey}", 
                context.HttpContext.Connection.RemoteIpAddress, 
                extractedApiKey.ToString().Substring(0, Math.Min(8, extractedApiKey.ToString().Length)) + "...");
            
            context.Result = new UnauthorizedObjectResult(new { 
                error = "Invalid API key",
                message = "Access denied" 
            });
            return;
        }
        
        logger.LogInformation("Successful API key authentication from {RemoteIpAddress}", 
            context.HttpContext.Connection.RemoteIpAddress);
    }
}
