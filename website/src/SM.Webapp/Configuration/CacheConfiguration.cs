using Microsoft.Extensions.Caching.Memory;

namespace SM.Webapp.Configuration;

/// <summary>
/// Cache configuration optimized for shared hosting environments
/// </summary>
public static class CacheConfiguration
{
    /// <summary>
    /// Configure memory cache based on hosting environment
    /// </summary>
    public static void ConfigureMemoryCache(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
    {
        var cacheSettings = GetCacheSettings(configuration, environment);
        
        services.AddMemoryCache(options =>
        {
            options.SizeLimit = cacheSettings.MaxEntries;
            options.CompactionPercentage = cacheSettings.CompactionPercentage;
            options.ExpirationScanFrequency = TimeSpan.FromMinutes(cacheSettings.ScanFrequencyMinutes);
        });
    }

    /// <summary>
    /// Get cache settings based on environment
    /// </summary>
    private static CacheSettings GetCacheSettings(IConfiguration configuration, IWebHostEnvironment environment)
    {
        // Try to get from configuration first
        var configSettings = configuration.GetSection("Cache").Get<CacheSettings>();
        if (configSettings != null)
        {
            return configSettings;
        }

        // Fallback to environment-based defaults
        return environment.IsDevelopment() 
            ? GetDevelopmentCacheSettings() 
            : GetProductionCacheSettings();
    }

    /// <summary>
    /// Development cache settings - more generous
    /// </summary>
    private static CacheSettings GetDevelopmentCacheSettings()
    {
        return new CacheSettings
        {
            MaxEntries = 200,
            CompactionPercentage = 0.25,
            ScanFrequencyMinutes = 2,
            DefaultDurationMinutes = 5,
            LongDurationMinutes = 30,
            ShortDurationMinutes = 2
        };
    }

    /// <summary>
    /// Production cache settings - conservative for shared hosting
    /// </summary>
    private static CacheSettings GetProductionCacheSettings()
    {
        return new CacheSettings
        {
            MaxEntries = 50, // Very conservative for shared hosting
            CompactionPercentage = 0.30, // Remove 30% when limit reached
            ScanFrequencyMinutes = 10, // Less frequent scanning
            DefaultDurationMinutes = 15, // Shorter default duration
            LongDurationMinutes = 60, // Reduced long duration
            ShortDurationMinutes = 5 // Slightly longer short duration
        };
    }

    /// <summary>
    /// Create cache entry options with size and priority
    /// </summary>
    public static MemoryCacheEntryOptions CreateCacheOptions(
        TimeSpan duration, 
        CacheItemPriority priority = CacheItemPriority.Normal,
        int size = 1)
    {
        return new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = duration,
            Size = size,
            Priority = priority
        };
    }

    /// <summary>
    /// Get cache duration based on data type
    /// </summary>
    public static TimeSpan GetCacheDuration(CacheDataType dataType, CacheSettings settings)
    {
        return dataType switch
        {
            CacheDataType.Static => TimeSpan.FromMinutes(settings.LongDurationMinutes),
            CacheDataType.SemiStatic => TimeSpan.FromMinutes(settings.DefaultDurationMinutes),
            CacheDataType.Dynamic => TimeSpan.FromMinutes(settings.ShortDurationMinutes),
            _ => TimeSpan.FromMinutes(settings.DefaultDurationMinutes)
        };
    }

    /// <summary>
    /// Get cache priority based on data importance
    /// </summary>
    public static CacheItemPriority GetCachePriority(CacheDataType dataType)
    {
        return dataType switch
        {
            CacheDataType.Static => CacheItemPriority.High,
            CacheDataType.SemiStatic => CacheItemPriority.Normal,
            CacheDataType.Dynamic => CacheItemPriority.Low,
            _ => CacheItemPriority.Normal
        };
    }
}

/// <summary>
/// Cache settings configuration
/// </summary>
public class CacheSettings
{
    public int MaxEntries { get; set; } = 50;
    public double CompactionPercentage { get; set; } = 0.25;
    public int ScanFrequencyMinutes { get; set; } = 5;
    public int DefaultDurationMinutes { get; set; } = 15;
    public int LongDurationMinutes { get; set; } = 60;
    public int ShortDurationMinutes { get; set; } = 5;
}

/// <summary>
/// Cache data type for determining duration and priority
/// </summary>
public enum CacheDataType
{
    Static,     // Rarely changes (social media, contact info)
    SemiStatic, // Changes occasionally (navigation, languages)
    Dynamic     // Changes frequently (home slider, products)
}
