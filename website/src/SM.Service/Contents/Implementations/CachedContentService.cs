using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using SM.Service.Contents.Contracts;
using SM.Service.Contents.DTOs;

namespace SM.Service.Contents.Implementations;

/// <summary>
/// Cached wrapper for ContentService to improve performance
/// </summary>
public class CachedContentService : IContentService
{
    private readonly IContentService _contentService;
    private readonly IMemoryCache _cache;
    private readonly ILogger<CachedContentService> _logger;
    
    // Cache durations
    private static readonly TimeSpan ShortCacheDuration = TimeSpan.FromMinutes(5);
    private static readonly TimeSpan MediumCacheDuration = TimeSpan.FromMinutes(30);
    private static readonly TimeSpan LongCacheDuration = TimeSpan.FromHours(2);

    public CachedContentService(
        IContentService contentService, 
        IMemoryCache cache,
        ILogger<CachedContentService> logger)
    {
        _contentService = contentService;
        _cache = cache;
        _logger = logger;
    }

    public async Task<IEnumerable<SocialMediaAccountAddressDto>> GetSocialMediaAccountAddresses()
    {
        const string cacheKey = "social_media_accounts";
        
        if (_cache.TryGetValue(cacheKey, out IEnumerable<SocialMediaAccountAddressDto> cached))
        {
            _logger.LogDebug("Cache hit for {CacheKey}", cacheKey);
            return cached;
        }

        _logger.LogDebug("Cache miss for {CacheKey}, fetching from database", cacheKey);
        var result = await _contentService.GetSocialMediaAccountAddresses();
        
        _cache.Set(cacheKey, result, new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = LongCacheDuration,
            Size = 1, // Each entry counts as 1 unit
            Priority = CacheItemPriority.High // High priority for social media
        });
        return result;
    }

    public async Task<IEnumerable<LanguageDto>> GetLanguages(string currentCultureInfo)
    {
        var cacheKey = $"languages_{currentCultureInfo}";
        
        if (_cache.TryGetValue(cacheKey, out IEnumerable<LanguageDto> cached))
        {
            _logger.LogDebug("Cache hit for {CacheKey}", cacheKey);
            return cached;
        }

        _logger.LogDebug("Cache miss for {CacheKey}, fetching from database", cacheKey);
        var result = await _contentService.GetLanguages(currentCultureInfo);
        
        _cache.Set(cacheKey, result, new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = MediumCacheDuration,
            Size = 1,
            Priority = CacheItemPriority.High // High priority for languages
        });
        return result;
    }

    public async Task<IEnumerable<NavigationDto>> GetNavigation(string currentCultureInfo)
    {
        var cacheKey = $"navigation_{currentCultureInfo}";
        
        if (_cache.TryGetValue(cacheKey, out IEnumerable<NavigationDto> cached))
        {
            _logger.LogDebug("Cache hit for {CacheKey}", cacheKey);
            return cached;
        }

        _logger.LogDebug("Cache miss for {CacheKey}, fetching from database", cacheKey);
        var result = await _contentService.GetNavigation(currentCultureInfo);
        
        _cache.Set(cacheKey, result, MediumCacheDuration);
        return result;
    }

    public async Task<string> GetNavigationHTML(string currentCultureInfo)
    {
        var cacheKey = $"navigation_html_{currentCultureInfo}";
        
        if (_cache.TryGetValue(cacheKey, out string cached))
        {
            _logger.LogDebug("Cache hit for {CacheKey}", cacheKey);
            return cached;
        }

        _logger.LogDebug("Cache miss for {CacheKey}, fetching from database", cacheKey);
        var result = await _contentService.GetNavigationHTML(currentCultureInfo);
        
        _cache.Set(cacheKey, result, MediumCacheDuration);
        return result;
    }

    public async Task<IEnumerable<HomeSliderDto>> GetHomeSlider(string currentCultureInfo)
    {
        var cacheKey = $"home_slider_{currentCultureInfo}";
        
        if (_cache.TryGetValue(cacheKey, out IEnumerable<HomeSliderDto> cached))
        {
            _logger.LogDebug("Cache hit for {CacheKey}", cacheKey);
            return cached;
        }

        _logger.LogDebug("Cache miss for {CacheKey}, fetching from database", cacheKey);
        var result = await _contentService.GetHomeSlider(currentCultureInfo);
        
        _cache.Set(cacheKey, result, ShortCacheDuration);
        return result;
    }

    public async Task<GetAboutContentResponse> GetAboutContent(string currentCultureInfo)
    {
        var cacheKey = $"about_content_{currentCultureInfo}";
        
        if (_cache.TryGetValue(cacheKey, out GetAboutContentResponse cached))
        {
            _logger.LogDebug("Cache hit for {CacheKey}", cacheKey);
            return cached;
        }

        _logger.LogDebug("Cache miss for {CacheKey}, fetching from database", cacheKey);
        var result = await _contentService.GetAboutContent(currentCultureInfo);
        
        _cache.Set(cacheKey, result, LongCacheDuration);
        return result;
    }

    public async Task<GetContactInfoResponse> GetContactInfo(string currentCultureInfo)
    {
        var cacheKey = $"contact_info_{currentCultureInfo}";
        
        if (_cache.TryGetValue(cacheKey, out GetContactInfoResponse cached))
        {
            _logger.LogDebug("Cache hit for {CacheKey}", cacheKey);
            return cached;
        }

        _logger.LogDebug("Cache miss for {CacheKey}, fetching from database", cacheKey);
        var result = await _contentService.GetContactInfo(currentCultureInfo);
        
        _cache.Set(cacheKey, result, LongCacheDuration);
        return result;
    }
}
