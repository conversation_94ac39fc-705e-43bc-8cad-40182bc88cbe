using Microsoft.Extensions.DependencyInjection;
using SM.Service.Catalog.Contracts;
using SM.Service.Catalog.Implementations;
using SM.Service.Contents.Contracts;
using SM.Service.Contents.Implementations;
using SM.Service.Message.Implementations;

namespace SM.Service;

public static class Module
{
    public static IServiceCollection AddServiceModule(this IServiceCollection collection)
    {
        // Register base services
        collection.AddScoped<ContentService>();
        collection.AddScoped<CatalogService>();
        collection.AddScoped<IMessageService, MessageService>();

        // Register cached services as primary implementations
        collection.AddScoped<IContentService, CachedContentService>();
        collection.AddScoped<ICatalogService, CachedCatalogService>();

        return collection;
    }
}