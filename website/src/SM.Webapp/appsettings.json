{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Database": {"Provider": "SqlServer", "ConnectionString": "data source=***************\\MSSQLSERVER2022;initial catalog=sabunmutfagi_prod;user=sabunmutfagi_website;password=***************;MultipleActiveResultSets=True;TrustServerCertificate=True;"}, "ApiSettings": {"SitemapApiKey": "SM_2024_SitemapGen_SecureKey_9x7K2mP8qL"}, "Performance": {"SlowRequestThresholdMs": 1000, "CacheDefaultDurationMinutes": 30, "UseOptimizedServices": true}, "Cache": {"Enabled": true, "LowMemoryMode": false, "MaxEntries": 30, "CompactionPercentage": 0.4, "ScanFrequencyMinutes": 15, "DefaultDurationMinutes": 10, "LongDurationMinutes": 30, "ShortDurationMinutes": 3}}