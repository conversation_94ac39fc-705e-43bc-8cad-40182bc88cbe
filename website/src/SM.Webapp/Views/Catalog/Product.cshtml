@using SM.Webapp.Core
@using SM.Webapp.Resources
@model SM.Service.Catalog.DTOs.GetProductDetailResponse

@section StructuredData
{
    <!-- Product Schema for Product Detail Page -->
    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "Product",
        "name": "@Html.Raw(Model.Detail.Caption)",
        "description": "@Html.Raw(Model.Detail.Description)",
        "image": [
            @if (Model.Detail.ProductImages.Any())
            {
                @foreach (var image in Model.Detail.ProductImages)
                {
                    <text>"@("https://www.sabunmutfagi.com" + image.ImagePath)",</text>
                }
            }
        ],
        "url": "@("https://www.sabunmutfagi.com" + Context.Request.Path)",
        "brand": {
            "@@type": "Brand",
            "name": "@Html.Raw(LocalizerRes.page_meta_title_brand)",
            "url": "https://www.sabunmutfagi.com"
        },
        "manufacturer": {
            "@@type": "Organization",
            "name": "@Html.Raw(LocalizerRes.page_meta_title_brand)",
            "url": "https://www.sabunmutfagi.com"
        },
        "category": "@Model.Detail.ProductCategoryCaption",
        "breadcrumb": {
            "@@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@@type": "ListItem",
                    "position": 1,
                    "name": "@(LocalizerRes.navigation_home)",
                    "item": "https://www.sabunmutfagi.com/@LocalizerRes.language"
                },
                {
                    "@@type": "ListItem",
                    "position": 2,
                    "name": "@Html.Raw(Model.Detail.ProductCategoryCaption)",
                    "item": "https://www.sabunmutfagi.com/@LocalizerRes.language/@Model.Detail.ProductCategorySlug"
                },
                {
                    "@@type": "ListItem",
                    "position": 3,
                    "name": "@Html.Raw(Model.Detail.Caption)",
                    "item": "@("https://www.sabunmutfagi.com" + Context.Request.Path)"
                }
            ]
        }
    }
    </script>
}


@*@section Styles {
    <style>
   		/* Specific for This Page for Smooth Scrolling Product Image */
   		@@media (prefers-reduced-motion: no-preference) {
   			:root {
   				scroll-behavior: smooth;
   			}
   		}
   
   		.device-touch .section-single-features {
   			background-attachment: scroll !important;
   		}
   	</style>
}*@


<!-- Breadcrumb
============================================= -->
<div class="container">
    <nav aria-label="breadcrumb" class="my-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/@LocalizerRes.language">@LocalizerRes.navigation_home</a>
            </li>
            @if (!string.IsNullOrEmpty(Model.Detail.ProductCategorySlug))
            {
                <li class="breadcrumb-item">
                    <a href="/@LocalizerRes.language/@Model.Detail.ProductCategorySlug">@Html.Raw(Model.Detail.ProductCategoryCaption)</a>
                </li>
            }
            <li class="breadcrumb-item active" aria-current="page">
                @Html.Raw(Model.Detail.Caption)
            </li>
        </ol>
    </nav>
</div>

<!-- Hero
============================================= -->
<div class="container">
    <div class="row g-0 col-mb-50 swiper-vertical">
        <div class="col-md-6">
            <div class="row">
                <nav id="sticky-dots" class="d-none d-md-flex flex-column align-items-center col-auto one-page-menu" data-active-class="active" data-parent=".nav-link">

                    @foreach (var i in Model.Detail.ProductImages)
                    {
                        <a class="nav-link sticky-dot active" href="#<EMAIL>" data-href="#<EMAIL>">
                            <i class="bi-circle"></i>
                        </a>
                    }

                </nav>

                <div class="col scroll-container mt-5">
                    <div class="masonry-thumbs grid-container row row-cols-3 row-cols-md-1 masonry-gap-xl" data-lightbox="gallery">
                        @{int imageIndex = 1;}
                        @foreach (var i in Model.Detail.ProductImages)
                        {
                            <a href="@i.ImagePath" id="<EMAIL>" class="page-section p-0" data-lightbox="gallery-item" title="@Model.Detail.Caption - Görüntü @imageIndex">
                                <img src="@i.ImagePath" loading="lazy" alt="@Model.Detail.Caption sabunu detay görüntüsü @imageIndex">
                            </a>
                            imageIndex++;
                        }
                    </div>
                </div>
            </div>
        </div>
        <nav class="col-md-6 skincare-shop-desc">
            <div class="p-md-4 p-lg-5">

                <div class="d-flex align-items-center justify-content-between">
                    <p class="color text-uppercase ls-2 mb-3 small">
                        @Model.Detail.Title
                    </p>
                </div>

                <div class="d-flex align-items-center justify-content-between">
                    <h1 class="fs-1 lh-sm">@Model.Detail.Caption</h1>

                    <img src="@LocalizerRes.product_detail_handmadeicon" style=" max-width: 80px;" alt="@Html.Raw(LocalizerRes.product_handmade_icon_alt)"/>
                </div>
                <div class="line my-4" style="border-color: var(--cnvs-color);"></div>

                <!-- Product Single - Description Section
                ============================================= -->
                <h2 class="h4 mb-3">@Html.Raw(LocalizerRes.product_detail_description_heading)</h2>
                <p>@Model.Detail.Description</p>

                <!-- Product Single - Specifications Section
                ============================================= -->
                <h3 class="h5 mb-3 mt-4">@Html.Raw(LocalizerRes.product_detail_specifications_heading)</h3>

                <div class="row mb-3">
                    <span class="col-sm-3 fw-semibold">@LocalizerRes.product_detail_ingredients:</span>
                    <span class="col-sm-9">@Model.Detail.Ingredients</span>
                </div>

                <div class="row mb-3">
                    <span class="col-sm-3 fw-semibold">@LocalizerRes.product_detail_howtoapply:</span>
                    <span class="col-sm-9">@Model.Detail.HowToApply</span>
                </div>

                <div class="row mb-3">
                    <span class="col-sm-3 fw-semibold">@LocalizerRes.product_detail_dimension_title:</span>
                    <span class="col-sm-9">@Model.Detail.Dimensions</span>
                </div>
                <div class="row mb-3">
                    <span class="col-sm-3 fw-semibold">@LocalizerRes.product_detail_warning_title:</span>
                    <span class="col-sm-9">@Model.Detail.Warnings</span>
                </div>

                <div class="line my-4"></div>

                <!--  Share ============================================= -->
                <div class="mb-3 d-flex justify-content-between align-items-center">
                    <span>Share:</span>

                    <div class="social-icons">
                        @foreach (var m in Model.SocialMediaAccountAddresses)
                        {
                            <a href="@m.ShareLink.Replace("URL", Context.Request.GetUri())" class="social-icon rounded-circle bg-light si-mini @m.BgIcon" target="_blank">
                                <i class="fa-brands @m.Icon"></i>
                                <i class="fa-brands @m.Icon"></i>
                            </a>
                        }
                    </div>

                </div><!-- Portfolio Single - Share End -->

            </div>
        </nav>
    </div>
</div>