using System.Globalization;
using System.Security.Cryptography;
using AspNetCore.Mvc.Routing.Localization.Extensions;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.EntityFrameworkCore;
using SM.Data;
using SM.Localization;
using SM.Service;
using SM.Webapp;
using SM.Webapp.Configuration;
using SM.Webapp.Middleware;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddDbContext<SMDbContext>(optionsBuilder =>
    optionsBuilder.UseSqlServer(
        "data source=***************\\MSSQLSERVER2022;initial catalog=sabunmutfagi_prod;user=sabunmutfagi_website;password=***************;MultipleActiveResultSets=True;TrustServerCertificate=True;Connection Timeout=30;Command Timeout=60;Pooling=true;Min Pool Size=5;Max Pool Size=100;",
        options =>
        {
            options.EnableRetryOnFailure(maxRetryCount: 3, maxRetryDelay: TimeSpan.FromSeconds(5), errorNumbersToAdd: null);
            options.CommandTimeout(60);
        }));

// Configure memory cache based on hosting environment
builder.Services.ConfigureMemoryCache(builder.Configuration, builder.Environment);

builder.Services.AddServiceModule(builder.Configuration);

var supportedCultures = new[]
{
    new CultureInfo("tr"),
    new CultureInfo("en"),
    new CultureInfo("ru"),
    new CultureInfo("de"),
    new CultureInfo("ar")
};

builder.Services.AddLocalizedRouting(supportedCultures);
builder.Services.AddSingleton<LocalizedRoutingTranslationTransformer>();
//builder.Services.AddLocalization();
builder.Services.AddLocalization(options => { options.ResourcesPath = "Resources"; });

// Add services to the container.
builder.Services.AddControllersWithViews()
    .AddViewLocalization(LanguageViewLocationExpanderFormat.Suffix);

builder.Services.AddScoped<ErrorHandlerMiddleware>();
// builder.Services.AddScoped<ExceptionFilter>();
// builder.Services.AddScoped<GlobalActionFilter>();

// Add response caching and compression
builder.Services.AddResponseCaching();
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<Microsoft.AspNetCore.ResponseCompression.BrotliCompressionProvider>();
    options.Providers.Add<Microsoft.AspNetCore.ResponseCompression.GzipCompressionProvider>();
    options.MimeTypes = Microsoft.AspNetCore.ResponseCompression.ResponseCompressionDefaults.MimeTypes.Concat(
        new[] { "application/javascript", "text/css", "text/html", "application/xml", "text/xml", "application/json", "text/json" });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/Error");
    app.UseStatusCodePagesWithReExecute("/Error/{0}");

    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.Use(async (context, next) => {
    
    var rng = new RNGCryptoServiceProvider();
    var nonceBytes = new byte[32];
    rng.GetBytes(nonceBytes);
    var nonce = Convert.ToBase64String(nonceBytes);
    context.Items.Add("ScriptNonce", nonce);
    context.Response.Headers.Add("Content-Security-Policy",
        new[] { string.Format("script-src 'self' 'nonce-{0}'", nonce) });
    
    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Remove("X-Powered-By");
    
    if (context.Request.Path.StartsWithSegments("/robots.txt") || context.Request.Path.StartsWithSegments("/Robots.txt")) {
        var robotsTxtPath = "Robots.txt";
        string output = "User-agent: *  \nAllow: /";
        if (File.Exists(robotsTxtPath)) {
            output = await File.ReadAllTextAsync(robotsTxtPath);
        }
        context.Response.ContentType = "text/plain";
        await context.Response.WriteAsync(output);
    }
    else if (context.Request.Path.StartsWithSegments("/sitemap.xml")) {
        var sitemapPath = Path.Combine("wwwroot", "sitemap.xml");
        if (File.Exists(sitemapPath)) {
            var content = await File.ReadAllTextAsync(sitemapPath);
            context.Response.ContentType = "application/xml";
            await context.Response.WriteAsync(content);
        } else {
            context.Response.StatusCode = 404;
            await context.Response.WriteAsync("Sitemap not found. Please generate it first using /generate-sitemap");
        }
    }
    else
    {
        await next();
    }
});


app.UseHttpsRedirection();
app.UseResponseCompression();
app.UseStaticFiles(new StaticFileOptions
{
    OnPrepareResponse = ctx =>
    {
        // Cache static files for 1 year
        ctx.Context.Response.Headers.Append("Cache-Control", "public,max-age=********");
        ctx.Context.Response.Headers.Append("Expires", DateTime.UtcNow.AddYears(1).ToString("R"));
    }
});

// Performance monitoring middleware
app.UseMiddleware<PerformanceMiddleware>();
// global error handler
app.UseMiddleware<ErrorHandlerMiddleware>();
// URL redirect middleware for SEO-friendly URLs
app.UseMiddleware<UrlRedirectMiddleware>();

app.UseRouting();
app.UseLocalizedRouting("tr", supportedCultures);
app.UseAuthorization();

// Explicit static page routes (highest priority)
app.MapControllerRoute(
    name: "AboutTr",
    pattern: "tr/hakkimizda",
    defaults: new { controller = "About", action = "Index", culture = "tr" });

app.MapControllerRoute(
    name: "AboutEn",
    pattern: "en/about",
    defaults: new { controller = "About", action = "Index", culture = "en" });

app.MapControllerRoute(
    name: "AboutRu",
    pattern: "ru/о-нас",
    defaults: new { controller = "About", action = "Index", culture = "ru" });

app.MapControllerRoute(
    name: "ContactTr",
    pattern: "tr/iletisim",
    defaults: new { controller = "Contact", action = "Index", culture = "tr" });

app.MapControllerRoute(
    name: "ContactEn",
    pattern: "en/contact",
    defaults: new { controller = "Contact", action = "Index", culture = "en" });

app.MapControllerRoute(
    name: "ContactRu",
    pattern: "ru/контакт",
    defaults: new { controller = "Contact", action = "Index", culture = "ru" });

// SEO-friendly catalog routes - dynamic resolution (medium priority)
app.MapControllerRoute(
    name: "CatalogDynamic",
    pattern: "{culture=tr}/{slug:minlength(1):maxlength(100)}/{productSlug:minlength(1):maxlength(100)?}",
    defaults: new { controller = "Catalog", action = "Dynamic" },
    constraints: new { culture = "tr|en|ru" });

app.MapControllerRoute(
    name: "CatalogDynamicEn",
    pattern: "{culture=en}/{slug:minlength(1):maxlength(100)}/{productSlug:minlength(1):maxlength(100)?}",
    defaults: new { controller = "Catalog", action = "Dynamic" },
    constraints: new { culture = "tr|en|ru" });

app.MapControllerRoute(
    name: "CatalogDynamicRu",
    pattern: "{culture=ru}/{slug:minlength(1):maxlength(100)}/{productSlug:minlength(1):maxlength(100)?}",
    defaults: new { controller = "Catalog", action = "Dynamic" },
    constraints: new { culture = "tr|en|ru" });

// Dynamic localized routing (lower priority)
app.MapDynamicControllerRoute<LocalizedRoutingTranslationTransformer>(
    "{culture=tr}/{controller=Home}/{action=Index}/{slug?}");

app.MapControllerRoute(
    "default",
    "{culture=tr}/{controller=Home}/{action=Index}/{slug?}");

// Fallback route for invalid URLs - redirect to 404
app.MapFallback(context =>
{
    // Don't handle static files, API calls, or already handled requests
    if (context.Request.Path.StartsWithSegments("/api") ||
        context.Request.Path.StartsWithSegments("/images") ||
        context.Request.Path.StartsWithSegments("/css") ||
        context.Request.Path.StartsWithSegments("/js") ||
        context.Request.Path.StartsWithSegments("/lib") ||
        context.Response.HasStarted)
    {
        return Task.CompletedTask;
    }

    context.Response.Redirect("/Error/404");
    return Task.CompletedTask;
});

app.UseResponseCaching();


app.Run();