{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Error", "SM.Service": "Warning"}}, "Database": {"Provider": "SqlServer", "ConnectionString": "data source=***************\\MSSQLSERVER2022;initial catalog=sabunmutfagi_prod;user=sabunmutfagi_website;password=***************;MultipleActiveResultSets=True;TrustServerCertificate=True;Connection Timeout=30;Command Timeout=60;Pooling=true;Min Pool Size=1;Max Pool Size=5;"}, "ApiSettings": {"SitemapApiKey": "SM_2024_SitemapGen_SecureKey_9x7K2mP8qL"}, "Performance": {"SlowRequestThresholdMs": 3000, "CacheDefaultDurationMinutes": 0}, "Cache": {"Enabled": false, "LowMemoryMode": true, "MaxEntries": 0, "CompactionPercentage": 1.0, "ScanFrequencyMinutes": 60, "DefaultDurationMinutes": 0, "LongDurationMinutes": 0, "ShortDurationMinutes": 0}}