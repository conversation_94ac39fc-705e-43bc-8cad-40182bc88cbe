using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using SM.Service.Contents.Contracts;
using SM.Service.Contents.DTOs;

namespace SM.Service.Contents.Implementations;

/// <summary>
/// Optimized content service with minimal database queries
/// </summary>
public class OptimizedContentService : SMServiceBase, IContentService
{
    public OptimizedContentService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public async Task<IEnumerable<SocialMediaAccountAddressDto>> GetSocialMediaAccountAddresses()
    {
        var content = await DbContext.Contents
            .Where(c => c.Key == Constants.SOCIAL_ACCOUNT_ADDRESS_LIST && !c.IsDeleted)
            .Select(c => c.Value)
            .AsNoTracking()
            .FirstOrDefaultAsync();

        return content != null
            ? JsonConvert.DeserializeObject<IEnumerable<SocialMediaAccountAddressDto>>(content)
            : new List<SocialMediaAccountAddressDto>();
    }

    public async Task<IEnumerable<LanguageDto>> GetLanguages(string currentCultureInfo)
    {
        return await DbContext.Languages
            .Where(l => l.IsActive && !l.IsDeleted)
            .Select(l => new LanguageDto
            {
                Caption = l.Caption,
                Prefix = l.Prefix,
                Url = "/" + l.Prefix,
                IsActive = l.Prefix == currentCultureInfo
            })
            .OrderBy(l => l.Prefix)
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<IEnumerable<NavigationDto>> GetNavigation(string currentCultureInfo)
    {
        return await DbContext.NavigationTranslations
            .Where(nt => nt.Navigation.IsActive 
                        && !nt.Navigation.IsDeleted 
                        && !nt.IsDeleted
                        && nt.LangPrefix == currentCultureInfo)
            .Select(nt => new NavigationDto
            {
                Id = nt.NavigationId,
                ParentId = nt.Navigation.ParentId,
                Caption = nt.Caption,
                Code = nt.Navigation.Code,
                SortPriority = nt.Navigation.SortPriority,
                IsTargetBlank = nt.Navigation.Type == NavigationType.Out,
                Url = Utils.PrepareUrl(nt.Navigation.Type, nt.LangPrefix, nt.Link)
            })
            .OrderBy(nt => nt.SortPriority)
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<string> GetNavigationHTML(string currentCultureInfo)
    {
        var navigations = await GetNavigation(currentCultureInfo);
        return BuildNavigationHTML(navigations);
    }

    public async Task<IEnumerable<HomeSliderDto>> GetHomeSlider(string currentCultureInfo)
    {
        var content = await DbContext.Contents
            .Where(c => c.Key == Constants.HOME_SLIDER && !c.IsDeleted)
            .Select(c => c.Value)
            .AsNoTracking()
            .FirstOrDefaultAsync();

        if (content == null)
            return new List<HomeSliderDto>();

        var sliders = JsonConvert.DeserializeObject<IEnumerable<HomeSliderDto>>(content);
        return sliders?.Where(s => s.LangPrefix == currentCultureInfo) ?? new List<HomeSliderDto>();
    }

    public async Task<GetAboutContentResponse> GetAboutContent(string currentCultureInfo)
    {
        // Get content and languages in parallel
        var contentTask = DbContext.Contents
            .Where(c => c.Key == Constants.ABOUT_CONTENT && !c.IsDeleted)
            .Select(c => c.Value)
            .AsNoTracking()
            .FirstOrDefaultAsync();

        var languagesTask = GetLanguages(currentCultureInfo);

        await Task.WhenAll(contentTask, languagesTask);

        var content = await contentTask;
        var languages = await languagesTask;

        if (content == null)
            return new GetAboutContentResponse { Languages = languages };

        var contentObj = JsonConvert.DeserializeObject<AboutContentDto>(content);

        // Set language URLs
        var languageList = languages.ToList();
        foreach (var lang in languageList)
        {
            lang.Url = lang.Prefix switch
            {
                "tr" => "/tr/hakkimizda",
                "en" => "/en/about",
                "ru" => "/ru/о-нас",
                _ => "/tr/hakkimizda"
            };
        }

        return new GetAboutContentResponse
        {
            AboutContent = contentObj,
            Languages = languageList
        };
    }

    public async Task<GetContactInfoResponse> GetContactInfo(string currentCultureInfo)
    {
        // Get contact info and languages in parallel
        var contactInfoTask = DbContext.Contents
            .Where(c => c.Key == Constants.CONTACT_INFO && !c.IsDeleted)
            .Select(c => c.Value)
            .AsNoTracking()
            .FirstOrDefaultAsync();

        var languagesTask = GetLanguages(currentCultureInfo);

        await Task.WhenAll(contactInfoTask, languagesTask);

        var contactInfo = await contactInfoTask;
        var languages = await languagesTask;

        if (contactInfo == null)
            return new GetContactInfoResponse { Languages = languages };

        var contactInfoObj = JsonConvert.DeserializeObject<GetContactInfoResponse>(contactInfo);

        // Set language URLs
        var languageList = languages.ToList();
        foreach (var lang in languageList)
        {
            lang.Url = lang.Prefix switch
            {
                "tr" => "/tr/iletisim",
                "en" => "/en/contact",
                "ru" => "/ru/контакт",
                _ => "/tr/iletisim"
            };
        }

        contactInfoObj.Languages = languageList;
        return contactInfoObj;
    }

    private string BuildNavigationHTML(IEnumerable<NavigationDto> navigations)
    {
        var navList = navigations.ToList();
        var rootItems = navList.Where(n => n.ParentId == null).OrderBy(n => n.SortPriority);
        
        var html = new System.Text.StringBuilder();
        html.AppendLine("<ul class=\"navbar-nav\">");
        
        foreach (var item in rootItems)
        {
            BuildNavigationItem(html, item, navList);
        }
        
        html.AppendLine("</ul>");
        return html.ToString();
    }

    private void BuildNavigationItem(System.Text.StringBuilder html, NavigationDto item, List<NavigationDto> allItems)
    {
        var children = allItems.Where(n => n.ParentId == item.Id).OrderBy(n => n.SortPriority).ToList();
        var hasChildren = children.Any();
        
        html.AppendLine($"<li class=\"nav-item{(hasChildren ? " dropdown" : "")}\">");
        
        if (hasChildren)
        {
            html.AppendLine($"<a class=\"nav-link dropdown-toggle\" href=\"{item.Url}\" role=\"button\" data-bs-toggle=\"dropdown\">");
            html.AppendLine(item.Caption);
            html.AppendLine("</a>");
            html.AppendLine("<ul class=\"dropdown-menu\">");
            
            foreach (var child in children)
            {
                html.AppendLine("<li>");
                html.AppendLine($"<a class=\"dropdown-item\" href=\"{child.Url}\"{(child.IsTargetBlank ? " target=\"_blank\"" : "")}>");
                html.AppendLine(child.Caption);
                html.AppendLine("</a>");
                html.AppendLine("</li>");
            }
            
            html.AppendLine("</ul>");
        }
        else
        {
            html.AppendLine($"<a class=\"nav-link\" href=\"{item.Url}\"{(item.IsTargetBlank ? " target=\"_blank\"" : "")}>");
            html.AppendLine(item.Caption);
            html.AppendLine("</a>");
        }
        
        html.AppendLine("</li>");
    }
}
