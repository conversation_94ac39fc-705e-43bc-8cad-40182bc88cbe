{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "SM.Service": "Information"}}, "Database": {"Provider": "SqlServer", "ConnectionString": "data source=***************\\MSSQLSERVER2022;initial catalog=sabunmutfagi_prod;user=sabunmutfagi_website;password=***************;MultipleActiveResultSets=True;TrustServerCertificate=True;Connection Timeout=30;Command Timeout=60;Pooling=true;Min Pool Size=2;Max Pool Size=20;"}, "ApiSettings": {"SitemapApiKey": "SM_2024_SitemapGen_SecureKey_9x7K2mP8qL"}, "Performance": {"SlowRequestThresholdMs": 2000, "CacheDefaultDurationMinutes": 60}, "Cache": {"Enabled": true, "LowMemoryMode": false, "MaxEntries": 20, "CompactionPercentage": 0.5, "ScanFrequencyMinutes": 30, "DefaultDurationMinutes": 20, "LongDurationMinutes": 60, "ShortDurationMinutes": 5}}