using Microsoft.AspNetCore.Mvc;
using SM.Service.Catalog.Contracts;
using SM.Service.Contents.Contracts;
using SM.Webapp.Attributes;
using System.Text;
using System.Xml;

namespace SM.Webapp.Controllers;

public class SitemapController : Controller
{
    private readonly ICatalogService _catalogService;
    private readonly IContentService _contentService;
    private readonly ILogger<SitemapController> _logger;
    private readonly string _baseUrl = "https://www.sabunmutfagi.com";

    public SitemapController(ICatalogService catalogService, IContentService contentService, ILogger<SitemapController> logger)
    {
        _catalogService = catalogService;
        _contentService = contentService;
        _logger = logger;
    }

    /// <summary>
    /// Manuel sitemap generation endpoint - API key authentication required
    /// URL: /generate-sitemap
    /// Header: X-API-Key: your-secret-key
    /// </summary>
    [Route("generate-sitemap")]
    [ApiKeyAuth]
    public async Task<IActionResult> GenerateSitemap()
    {
        try
        {
            _logger.LogInformation("Starting sitemap generation...");
            var sitemap = await GenerateSitemapAsync();

            // Dosyayı wwwroot'a kaydet
            var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "sitemap.xml");
            await System.IO.File.WriteAllTextAsync(filePath, sitemap, Encoding.UTF8);

            _logger.LogInformation("Sitemap generated and saved to {FilePath}, Size: {Size} bytes", filePath, sitemap.Length);

            return Json(new {
                success = true,
                message = "Sitemap generated successfully!",
                filePath = "/sitemap.xml",
                generatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                sizeBytes = sitemap.Length,
                sizeKB = Math.Round(sitemap.Length / 1024.0, 2)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating sitemap");
            return Json(new {
                success = false,
                message = $"Error: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Sitemap preview endpoint - API key authentication required
    /// URL: /preview-sitemap
    /// Header: X-API-Key: your-secret-key
    /// </summary>
    [Route("preview-sitemap")]
    [ApiKeyAuth]
    public async Task<IActionResult> PreviewSitemap()
    {
        try
        {
            var sitemap = await GenerateSitemapAsync();
            return Content(sitemap, "application/xml", Encoding.UTF8);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating sitemap preview");
            return Content($"<!-- Error: {ex.Message} -->", "text/plain");
        }
    }

    /// <summary>
    /// Test endpoint to verify API key authentication
    /// URL: /test-sitemap-auth
    /// Header: X-API-Key: your-secret-key
    /// </summary>
    [Route("test-sitemap-auth")]
    [ApiKeyAuth]
    public IActionResult TestAuth()
    {
        return Json(new {
            success = true,
            message = "API key authentication successful!",
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            remoteIp = HttpContext.Connection.RemoteIpAddress?.ToString()
        });
    }

    private async Task<string> GenerateSitemapAsync()
    {
        var sb = new StringBuilder();
        
        // XML header
        sb.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        sb.AppendLine("<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">");

        var lastMod = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");

        // Add homepage URLs
        AddHomepageUrls(sb, lastMod);

        // Add static page URLs
        await AddStaticPageUrls(sb, lastMod);

        // Add category URLs
        await AddCategoryUrls(sb, lastMod);

        // Add product URLs
        await AddProductUrls(sb, lastMod);

        // Add brochure URLs
        AddBrochureUrls(sb, lastMod);

        sb.AppendLine("</urlset>");
        
        return sb.ToString();
    }

    private void AddHomepageUrls(StringBuilder sb, string lastMod)
    {
        // Main homepage
        AddUrl(sb, _baseUrl, lastMod, "1.00");
        
        // Language homepages
        AddUrl(sb, $"{_baseUrl}/tr", lastMod, "0.80");
        AddUrl(sb, $"{_baseUrl}/en", lastMod, "0.80");
        AddUrl(sb, $"{_baseUrl}/ru", lastMod, "0.80");
    }

    private async Task AddStaticPageUrls(StringBuilder sb, string lastMod)
    {
        var languages = new[] { "tr", "en", "ru" };
        
        foreach (var lang in languages)
        {
            // About pages
            var aboutUrl = lang switch
            {
                "tr" => "hakkimizda",
                "en" => "about", 
                "ru" => "о-нас",
                _ => "hakkimizda"
            };
            AddUrl(sb, $"{_baseUrl}/{lang}/{aboutUrl}", lastMod, "0.80");

            // Contact pages
            var contactUrl = lang switch
            {
                "tr" => "iletisim",
                "en" => "contact",
                "ru" => "контакт",
                _ => "iletisim"
            };
            AddUrl(sb, $"{_baseUrl}/{lang}/{contactUrl}", lastMod, "0.80");
        }
    }

    private async Task AddCategoryUrls(StringBuilder sb, string lastMod)
    {
        var languages = new[] { "tr", "en", "ru" };
        
        foreach (var lang in languages)
        {
            try
            {
                var categories = await _catalogService.GetProductCategory(lang);
                
                foreach (var category in categories)
                {
                    // Extract clean slug from the URL
                    var cleanSlug = ExtractSlugFromUrl(category.Slug);
                    if (!string.IsNullOrEmpty(cleanSlug))
                    {
                        // New clean URL structure: /{lang}/{category-slug}
                        AddUrl(sb, $"{_baseUrl}/{lang}/{cleanSlug}", lastMod, "0.80");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error adding category URLs for language: {Language}", lang);
            }
        }
    }

    private async Task AddProductUrls(StringBuilder sb, string lastMod)
    {
        var languages = new[] { "tr", "en", "ru" };

        foreach (var lang in languages)
        {
            try
            {
                var categories = await _catalogService.GetProductCategory(lang);

                foreach (var category in categories)
                {
                    var categorySlug = ExtractSlugFromUrl(category.Slug);
                    if (string.IsNullOrEmpty(categorySlug)) continue;

                    try
                    {
                        var productsResponse = await _catalogService.GetProducts(new SM.Service.Catalog.DTOs.GetProductsRequest
                        {
                            CurrentCultureInfo = lang,
                            Slug = categorySlug
                        });

                        if (productsResponse?.Products != null)
                        {
                            foreach (var product in productsResponse.Products)
                            {
                                if (!string.IsNullOrEmpty(product.Slug))
                                {
                                    // New clean URL structure: /{lang}/{category-slug}/{product-slug}
                                    AddUrl(sb, $"{_baseUrl}/{lang}/{categorySlug}/{product.Slug}", lastMod, "0.64");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error adding product URLs for category: {CategorySlug} in language: {Language}", categorySlug, lang);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing products for language: {Language}", lang);
            }
        }
    }

    private void AddBrochureUrls(StringBuilder sb, string lastMod)
    {
        // Static brochure URLs
        AddUrl(sb, $"{_baseUrl}/download/brochure/sabun-mutfagi-brochure-2023-tr.pdf", "2023-10-17T20:34:42Z", "0.80");
        AddUrl(sb, $"{_baseUrl}/download/brochure/sabun-mutfagi-brochure-2023-en.pdf", "2023-10-17T20:33:59Z", "0.64");
        AddUrl(sb, $"{_baseUrl}/download/brochure/sabun-mutfagi-brochure-2023-ru.pdf", "2023-10-17T20:34:33Z", "0.64");
    }

    private void AddUrl(StringBuilder sb, string url, string lastMod, string priority)
    {
        sb.AppendLine("  <url>");
        sb.AppendLine($"    <loc>{XmlEscape(url)}</loc>");
        sb.AppendLine($"    <lastmod>{lastMod}</lastmod>");
        sb.AppendLine($"    <priority>{priority}</priority>");
        sb.AppendLine("  </url>");
    }

    private string ExtractSlugFromUrl(string url)
    {
        if (string.IsNullOrEmpty(url)) return string.Empty;
        
        // Remove leading slash and extract the last segment
        var segments = url.Trim('/').Split('/');
        return segments.LastOrDefault() ?? string.Empty;
    }

    private string XmlEscape(string text)
    {
        if (string.IsNullOrEmpty(text)) return string.Empty;
        
        return text.Replace("&", "&amp;")
                  .Replace("<", "&lt;")
                  .Replace(">", "&gt;")
                  .Replace("\"", "&quot;")
                  .Replace("'", "&apos;");
    }
}
