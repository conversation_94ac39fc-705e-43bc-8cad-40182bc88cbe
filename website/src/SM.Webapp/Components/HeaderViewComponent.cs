using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc;
using SM.Service.Contents.Contracts;
using SM.Webapp.Core;
using SM.Webapp.Models;

namespace SM.WebApp.Components;

public class HeaderViewComponent : ViewComponent
{
    private readonly IContentService ContentService;

    public HeaderViewComponent(IContentService contentService)
    {
        ContentService = contentService;
    }

    public async Task<IViewComponentResult> InvokeAsync()
    {
        var lang = RouteData.GetLanguage();

        // Parallel async calls for better performance
        var socialMediaTask = ContentService.GetSocialMediaAccountAddresses();
        var languagesTask = ContentService.GetLanguages(lang);
        var navigationTask = ContentService.GetNavigationHTML(lang);

        await Task.WhenAll(socialMediaTask, languagesTask, navigationTask);

        return View(new HeaderViewModel
        {
            SocialMediaAccountAddresses = await socialMediaTask,
            Languages = await languagesTask,
            NavigationHTML = new HtmlString(await navigationTask)
        });
    }
}