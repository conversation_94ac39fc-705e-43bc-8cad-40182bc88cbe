using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SM.Data.Migrations
{
    /// <inheritdoc />
    public partial class PerformanceIndexes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Composite indexes for better query performance
            
            // ProductTranslations - most critical for slug lookups
            migrationBuilder.CreateIndex(
                name: "IX_ProductTranslations_Slug_LangPrefix_IsDeleted",
                table: "ProductTranslations",
                columns: new[] { "Slug", "LangPrefix", "IsDeleted" });
                
            migrationBuilder.CreateIndex(
                name: "IX_ProductTranslations_ProductId_LangPrefix_IsDeleted",
                table: "ProductTranslations",
                columns: new[] { "ProductId", "LangPrefix", "IsDeleted" });

            // ProductCategoryTranslations - for category lookups
            migrationBuilder.CreateIndex(
                name: "IX_ProductCategoryTranslations_Slug_LangPrefix_IsDeleted",
                table: "ProductCategoryTranslations",
                columns: new[] { "Slug", "LangPrefix", "IsDeleted" });
                
            migrationBuilder.CreateIndex(
                name: "IX_ProductCategoryTranslations_CategoryId_LangPrefix_IsDeleted",
                table: "ProductCategoryTranslations",
                columns: new[] { "CategoryId", "LangPrefix", "IsDeleted" });

            // Products - for active product filtering
            migrationBuilder.CreateIndex(
                name: "IX_Products_IsActive_IsDeleted",
                table: "Products",
                columns: new[] { "IsActive", "IsDeleted" });

            // ProductCategories - for active category filtering
            migrationBuilder.CreateIndex(
                name: "IX_ProductCategories_IsActive_IsDeleted",
                table: "ProductCategories",
                columns: new[] { "IsActive", "IsDeleted" });

            // ProductImages - for image filtering
            migrationBuilder.CreateIndex(
                name: "IX_ProductImages_ProductId_IsDeleted_IsActive",
                table: "ProductImages",
                columns: new[] { "ProductId", "IsDeleted", "IsActive" });
                
            migrationBuilder.CreateIndex(
                name: "IX_ProductImages_ProductId_IsMain_IsDeleted",
                table: "ProductImages",
                columns: new[] { "ProductId", "IsMain", "IsDeleted" });

            // ProductCategoryRelations - for product-category joins
            migrationBuilder.CreateIndex(
                name: "IX_ProductCategoryRelations_CategoryId_ProductId",
                table: "ProductCategoryRelations",
                columns: new[] { "CategoryId", "ProductId" });

            // NavigationTranslations - for navigation queries
            migrationBuilder.CreateIndex(
                name: "IX_NavigationTranslations_LangPrefix_IsDeleted",
                table: "NavigationTranslations",
                columns: new[] { "LangPrefix", "IsDeleted" });

            // Contents - for content key lookups
            migrationBuilder.CreateIndex(
                name: "IX_Contents_Key_IsDeleted",
                table: "Contents",
                columns: new[] { "Key", "IsDeleted" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ProductTranslations_Slug_LangPrefix_IsDeleted",
                table: "ProductTranslations");
                
            migrationBuilder.DropIndex(
                name: "IX_ProductTranslations_ProductId_LangPrefix_IsDeleted",
                table: "ProductTranslations");

            migrationBuilder.DropIndex(
                name: "IX_ProductCategoryTranslations_Slug_LangPrefix_IsDeleted",
                table: "ProductCategoryTranslations");
                
            migrationBuilder.DropIndex(
                name: "IX_ProductCategoryTranslations_CategoryId_LangPrefix_IsDeleted",
                table: "ProductCategoryTranslations");

            migrationBuilder.DropIndex(
                name: "IX_Products_IsActive_IsDeleted",
                table: "Products");

            migrationBuilder.DropIndex(
                name: "IX_ProductCategories_IsActive_IsDeleted",
                table: "ProductCategories");

            migrationBuilder.DropIndex(
                name: "IX_ProductImages_ProductId_IsDeleted_IsActive",
                table: "ProductImages");
                
            migrationBuilder.DropIndex(
                name: "IX_ProductImages_ProductId_IsMain_IsDeleted",
                table: "ProductImages");

            migrationBuilder.DropIndex(
                name: "IX_ProductCategoryRelations_CategoryId_ProductId",
                table: "ProductCategoryRelations");

            migrationBuilder.DropIndex(
                name: "IX_NavigationTranslations_LangPrefix_IsDeleted",
                table: "NavigationTranslations");

            migrationBuilder.DropIndex(
                name: "IX_Contents_Key_IsDeleted",
                table: "Contents");
        }
    }
}
