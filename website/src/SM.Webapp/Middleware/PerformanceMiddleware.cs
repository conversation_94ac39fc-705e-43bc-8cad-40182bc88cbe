using System.Diagnostics;

namespace SM.Webapp.Middleware;

/// <summary>
/// Performance monitoring middleware to track slow requests
/// </summary>
public class PerformanceMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PerformanceMiddleware> _logger;
    private readonly int _slowRequestThresholdMs;

    public PerformanceMiddleware(RequestDelegate next, ILogger<PerformanceMiddleware> logger, IConfiguration configuration)
    {
        _next = next;
        _logger = logger;
        _slowRequestThresholdMs = configuration.GetValue<int>("Performance:SlowRequestThresholdMs", 1000);
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var requestPath = context.Request.Path.Value;
        var requestMethod = context.Request.Method;
        
        try
        {
            await _next(context);
        }
        finally
        {
            stopwatch.Stop();
            var elapsedMs = stopwatch.ElapsedMilliseconds;
            
            // Log slow requests
            if (elapsedMs > _slowRequestThresholdMs)
            {
                _logger.LogWarning("Slow request detected: {Method} {Path} took {ElapsedMs}ms (Status: {StatusCode})",
                    requestMethod, requestPath, elapsedMs, context.Response.StatusCode);
            }
            else if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug("Request completed: {Method} {Path} took {ElapsedMs}ms (Status: {StatusCode})",
                    requestMethod, requestPath, elapsedMs, context.Response.StatusCode);
            }
            
            // Add performance header for debugging
            if (context.Response.HasStarted == false)
            {
                context.Response.Headers.Add("X-Response-Time", $"{elapsedMs}ms");
            }
        }
    }
}
