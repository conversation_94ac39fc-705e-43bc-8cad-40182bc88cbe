namespace SM.Webapp.Middleware;

public class ErrorHandlerMiddleware : IMiddleware
{
    private readonly ILogger<ErrorHandlerMiddleware> _logger;

    public ErrorHandlerMiddleware(ILogger<ErrorHandlerMiddleware> logger)
    {
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        try
        {
            await next(context);
        }
        catch (Exception error)
        {
            _logger.LogError(error, "An unhandled exception occurred. Path: {Path}", context.Request.Path);

            // Let the built-in exception handler deal with it
            throw;
        }
    }
}