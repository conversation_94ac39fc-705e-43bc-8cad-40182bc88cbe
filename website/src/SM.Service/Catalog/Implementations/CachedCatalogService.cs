using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using SM.Service.Catalog.Contracts;
using SM.Service.Catalog.DTOs;

namespace SM.Service.Catalog.Implementations;

/// <summary>
/// Cached wrapper for CatalogService to improve performance
/// </summary>
public class CachedCatalogService : ICatalogService
{
    private readonly ICatalogService _catalogService;
    private readonly IMemoryCache _cache;
    private readonly ILogger<CachedCatalogService> _logger;
    
    // Cache durations
    private static readonly TimeSpan ShortCacheDuration = TimeSpan.FromMinutes(10);
    private static readonly TimeSpan MediumCacheDuration = TimeSpan.FromMinutes(30);
    private static readonly TimeSpan LongCacheDuration = TimeSpan.FromHours(1);

    public CachedCatalogService(
        ICatalogService catalogService, 
        IMemoryCache cache,
        ILogger<CachedCatalogService> logger)
    {
        _catalogService = catalogService;
        _cache = cache;
        _logger = logger;
    }

    public async Task<GetProductsResponse> GetProducts(GetProductsRequest request)
    {
        var cacheKey = $"products_{request.CurrentCultureInfo}_{request.Slug}";
        
        if (_cache.TryGetValue(cacheKey, out GetProductsResponse cached))
        {
            _logger.LogDebug("Cache hit for {CacheKey}", cacheKey);
            return cached;
        }

        _logger.LogDebug("Cache miss for {CacheKey}, fetching from database", cacheKey);
        var result = await _catalogService.GetProducts(request);
        
        _cache.Set(cacheKey, result, MediumCacheDuration);
        return result;
    }

    public async Task<GetProductDetailResponse> GetProductDetail(GetProductDetailRequest request)
    {
        var cacheKey = $"product_detail_{request.CurrentCultureInfo}_{request.Slug}";
        
        if (_cache.TryGetValue(cacheKey, out GetProductDetailResponse cached))
        {
            _logger.LogDebug("Cache hit for {CacheKey}", cacheKey);
            return cached;
        }

        _logger.LogDebug("Cache miss for {CacheKey}, fetching from database", cacheKey);
        var result = await _catalogService.GetProductDetail(request);
        
        _cache.Set(cacheKey, result, MediumCacheDuration);
        return result;
    }

    public async Task<IEnumerable<ProductCategoryDto>> GetProductCategory(string currentCultureInfo)
    {
        var cacheKey = $"product_categories_{currentCultureInfo}";
        
        if (_cache.TryGetValue(cacheKey, out IEnumerable<ProductCategoryDto> cached))
        {
            _logger.LogDebug("Cache hit for {CacheKey}", cacheKey);
            return cached;
        }

        _logger.LogDebug("Cache miss for {CacheKey}, fetching from database", cacheKey);
        var result = await _catalogService.GetProductCategory(currentCultureInfo);
        
        _cache.Set(cacheKey, result, LongCacheDuration);
        return result;
    }

    public async Task<string> GetCategorySlugByProductSlugAsync(string productSlug, string langPrefix)
    {
        var cacheKey = $"category_slug_{productSlug}_{langPrefix}";
        
        if (_cache.TryGetValue(cacheKey, out string cached))
        {
            _logger.LogDebug("Cache hit for {CacheKey}", cacheKey);
            return cached;
        }

        _logger.LogDebug("Cache miss for {CacheKey}, fetching from database", cacheKey);
        var result = await _catalogService.GetCategorySlugByProductSlugAsync(productSlug, langPrefix);
        
        _cache.Set(cacheKey, result, LongCacheDuration);
        return result;
    }
}
