using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using SM.Service.Catalog.Contracts;
using SM.Service.Contents.Contracts;
using SM.Webapp.Attributes;
using System.Diagnostics;

namespace SM.Webapp.Controllers;

/// <summary>
/// Performance testing and monitoring controller
/// </summary>
[ApiKeyAuth]
public class PerformanceController : Controller
{
    private readonly IContentService _contentService;
    private readonly ICatalogService _catalogService;
    private readonly IMemoryCache _cache;
    private readonly ILogger<PerformanceController> _logger;

    public PerformanceController(
        IContentService contentService,
        ICatalogService catalogService,
        IMemoryCache cache,
        ILogger<PerformanceController> logger)
    {
        _contentService = contentService;
        _catalogService = catalogService;
        _cache = cache;
        _logger = logger;
    }

    /// <summary>
    /// Test home page performance
    /// URL: /performance/test-home
    /// Header: X-API-Key: your-secret-key
    /// </summary>
    [Route("performance/test-home")]
    public async Task<IActionResult> TestHomePerformance()
    {
        var stopwatch = Stopwatch.StartNew();
        var results = new Dictionary<string, object>();

        try
        {
            // Test individual components
            var componentTests = new Dictionary<string, Func<Task<object>>>
            {
                ["Languages"] = async () => await _contentService.GetLanguages("tr"),
                ["SocialMedia"] = async () => await _contentService.GetSocialMediaAccountAddresses(),
                ["Navigation"] = async () => await _contentService.GetNavigationHTML("tr"),
                ["HomeSlider"] = async () => await _contentService.GetHomeSlider("tr"),
                ["ProductCategories"] = async () => await _catalogService.GetProductCategory("tr")
            };

            foreach (var test in componentTests)
            {
                var componentStopwatch = Stopwatch.StartNew();
                try
                {
                    var result = await test.Value();
                    componentStopwatch.Stop();
                    
                    results[test.Key] = new
                    {
                        Success = true,
                        ElapsedMs = componentStopwatch.ElapsedMilliseconds,
                        DataCount = GetDataCount(result)
                    };
                }
                catch (Exception ex)
                {
                    componentStopwatch.Stop();
                    results[test.Key] = new
                    {
                        Success = false,
                        ElapsedMs = componentStopwatch.ElapsedMilliseconds,
                        Error = ex.Message
                    };
                }
            }

            stopwatch.Stop();

            return Json(new
            {
                Success = true,
                TotalElapsedMs = stopwatch.ElapsedMilliseconds,
                ComponentResults = results,
                CacheStats = GetCacheStats(),
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error in performance test");
            
            return Json(new
            {
                Success = false,
                TotalElapsedMs = stopwatch.ElapsedMilliseconds,
                Error = ex.Message,
                ComponentResults = results
            });
        }
    }

    /// <summary>
    /// Clear all cache
    /// URL: /performance/clear-cache
    /// Header: X-API-Key: your-secret-key
    /// </summary>
    [Route("performance/clear-cache")]
    public IActionResult ClearCache()
    {
        try
        {
            if (_cache is MemoryCache memoryCache)
            {
                var field = typeof(MemoryCache).GetField("_coherentState", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var coherentState = field?.GetValue(memoryCache);
                var entriesCollection = coherentState?.GetType()
                    .GetProperty("EntriesCollection", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var entries = (IDictionary?)entriesCollection?.GetValue(coherentState);
                
                var clearedCount = entries?.Count ?? 0;
                entries?.Clear();

                return Json(new
                {
                    Success = true,
                    Message = "Cache cleared successfully",
                    ClearedEntries = clearedCount,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }

            return Json(new
            {
                Success = false,
                Message = "Unable to clear cache - cache type not supported"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache");
            return Json(new
            {
                Success = false,
                Message = $"Error clearing cache: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Get cache statistics
    /// URL: /performance/cache-stats
    /// Header: X-API-Key: your-secret-key
    /// </summary>
    [Route("performance/cache-stats")]
    public IActionResult GetCacheStats()
    {
        return Json(GetCacheStats());
    }

    private object GetCacheStats()
    {
        try
        {
            if (_cache is MemoryCache memoryCache)
            {
                var field = typeof(MemoryCache).GetField("_coherentState", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var coherentState = field?.GetValue(memoryCache);
                var entriesCollection = coherentState?.GetType()
                    .GetProperty("EntriesCollection", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var entries = (IDictionary?)entriesCollection?.GetValue(coherentState);

                return new
                {
                    TotalEntries = entries?.Count ?? 0,
                    CacheType = "MemoryCache"
                };
            }

            return new { Message = "Cache statistics not available" };
        }
        catch
        {
            return new { Message = "Error retrieving cache statistics" };
        }
    }

    private int GetDataCount(object data)
    {
        return data switch
        {
            System.Collections.IEnumerable enumerable => enumerable.Cast<object>().Count(),
            string str => str.Length,
            _ => 1
        };
    }
}
