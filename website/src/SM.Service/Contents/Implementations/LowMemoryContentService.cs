using SM.Service.Contents.Contracts;
using SM.Service.Contents.DTOs;

namespace SM.Service.Contents.Implementations;

/// <summary>
/// Low memory version of ContentService - no caching for very limited hosting
/// Use this when RAM is extremely limited (< 512MB)
/// </summary>
public class LowMemoryContentService : IContentService
{
    private readonly ContentService _contentService;

    public LowMemoryContentService(ContentService contentService)
    {
        _contentService = contentService;
    }

    public async Task<IEnumerable<SocialMediaAccountAddressDto>> GetSocialMediaAccountAddresses()
    {
        return await _contentService.GetSocialMediaAccountAddresses();
    }

    public async Task<IEnumerable<LanguageDto>> GetLanguages(string currentCultureInfo)
    {
        return await _contentService.GetLanguages(currentCultureInfo);
    }

    public async Task<IEnumerable<NavigationDto>> GetNavigation(string currentCultureInfo)
    {
        return await _contentService.GetNavigation(currentCultureInfo);
    }

    public async Task<string> GetNavigationHTML(string currentCultureInfo)
    {
        return await _contentService.GetNavigationHTML(currentCultureInfo);
    }

    public async Task<IEnumerable<HomeSliderDto>> GetHomeSlider(string currentCultureInfo)
    {
        return await _contentService.GetHomeSlider(currentCultureInfo);
    }

    public async Task<GetAboutContentResponse> GetAboutContent(string currentCultureInfo)
    {
        return await _contentService.GetAboutContent(currentCultureInfo);
    }

    public async Task<GetContactInfoResponse> GetContactInfo(string currentCultureInfo)
    {
        return await _contentService.GetContactInfo(currentCultureInfo);
    }
}
