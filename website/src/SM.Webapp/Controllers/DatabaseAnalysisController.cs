using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SM.Data;
using SM.Webapp.Attributes;
using System.Diagnostics;
using System.Text;

namespace SM.Webapp.Controllers;

/// <summary>
/// Database performance analysis controller
/// </summary>
[ApiKeyAuth]
public class DatabaseAnalysisController : Controller
{
    private readonly SMDbContext _dbContext;
    private readonly ILogger<DatabaseAnalysisController> _logger;

    public DatabaseAnalysisController(SMDbContext dbContext, ILogger<DatabaseAnalysisController> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    /// <summary>
    /// Analyze database indexes and suggest optimizations
    /// URL: /database/analyze-indexes
    /// Header: X-API-Key: your-secret-key
    /// </summary>
    [Route("database/analyze-indexes")]
    public async Task<IActionResult> AnalyzeIndexes()
    {
        try
        {
            var analysis = new List<object>();

            // Check missing indexes for common queries
            var missingIndexes = await AnalyzeMissingIndexes();
            var unusedIndexes = await AnalyzeUnusedIndexes();
            var fragmentedIndexes = await AnalyzeFragmentedIndexes();
            var tableStats = await AnalyzeTableStatistics();

            return Json(new
            {
                Success = true,
                Analysis = new
                {
                    MissingIndexes = missingIndexes,
                    UnusedIndexes = unusedIndexes,
                    FragmentedIndexes = fragmentedIndexes,
                    TableStatistics = tableStats
                },
                Recommendations = GenerateRecommendations(missingIndexes, unusedIndexes, fragmentedIndexes),
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing database indexes");
            return Json(new { Success = false, Error = ex.Message });
        }
    }

    /// <summary>
    /// Test query performance
    /// URL: /database/test-queries
    /// Header: X-API-Key: your-secret-key
    /// </summary>
    [Route("database/test-queries")]
    public async Task<IActionResult> TestQueryPerformance()
    {
        try
        {
            var results = new List<object>();

            // Test common queries
            var queries = new Dictionary<string, Func<Task<object>>>
            {
                ["ProductBySlug"] = async () => await TestProductBySlugQuery(),
                ["CategoryProducts"] = async () => await TestCategoryProductsQuery(),
                ["Navigation"] = async () => await TestNavigationQuery(),
                ["Languages"] = async () => await TestLanguagesQuery(),
                ["SocialMedia"] = async () => await TestSocialMediaQuery()
            };

            foreach (var query in queries)
            {
                var stopwatch = Stopwatch.StartNew();
                try
                {
                    var result = await query.Value();
                    stopwatch.Stop();
                    
                    results.Add(new
                    {
                        QueryName = query.Key,
                        Success = true,
                        ElapsedMs = stopwatch.ElapsedMilliseconds,
                        Result = result
                    });
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    results.Add(new
                    {
                        QueryName = query.Key,
                        Success = false,
                        ElapsedMs = stopwatch.ElapsedMilliseconds,
                        Error = ex.Message
                    });
                }
            }

            return Json(new
            {
                Success = true,
                QueryResults = results,
                Summary = new
                {
                    TotalQueries = results.Count,
                    SuccessfulQueries = results.Count(r => (bool)r.GetType().GetProperty("Success").GetValue(r)),
                    AverageTime = results.Where(r => (bool)r.GetType().GetProperty("Success").GetValue(r))
                                        .Average(r => (long)r.GetType().GetProperty("ElapsedMs").GetValue(r)),
                    SlowestQuery = results.OrderByDescending(r => (long)r.GetType().GetProperty("ElapsedMs").GetValue(r)).FirstOrDefault()
                },
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing query performance");
            return Json(new { Success = false, Error = ex.Message });
        }
    }

    /// <summary>
    /// Generate database optimization script
    /// URL: /database/optimization-script
    /// Header: X-API-Key: your-secret-key
    /// </summary>
    [Route("database/optimization-script")]
    public async Task<IActionResult> GenerateOptimizationScript()
    {
        try
        {
            var script = new StringBuilder();
            script.AppendLine("-- Database Optimization Script");
            script.AppendLine("-- Generated: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            script.AppendLine();

            // Add index creation scripts
            script.AppendLine("-- Performance Indexes");
            script.AppendLine("IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ProductTranslations_Slug_LangPrefix_IsDeleted')");
            script.AppendLine("CREATE NONCLUSTERED INDEX [IX_ProductTranslations_Slug_LangPrefix_IsDeleted] ON [ProductTranslations] ([Slug], [LangPrefix], [IsDeleted]);");
            script.AppendLine();

            script.AppendLine("IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ProductCategoryTranslations_Slug_LangPrefix_IsDeleted')");
            script.AppendLine("CREATE NONCLUSTERED INDEX [IX_ProductCategoryTranslations_Slug_LangPrefix_IsDeleted] ON [ProductCategoryTranslations] ([Slug], [LangPrefix], [IsDeleted]);");
            script.AppendLine();

            script.AppendLine("-- Update Statistics");
            script.AppendLine("UPDATE STATISTICS [Products];");
            script.AppendLine("UPDATE STATISTICS [ProductTranslations];");
            script.AppendLine("UPDATE STATISTICS [ProductCategories];");
            script.AppendLine("UPDATE STATISTICS [ProductCategoryTranslations];");
            script.AppendLine();

            script.AppendLine("-- Rebuild Fragmented Indexes (if fragmentation > 30%)");
            script.AppendLine("DECLARE @sql NVARCHAR(MAX) = '';");
            script.AppendLine("SELECT @sql = @sql + 'ALTER INDEX ' + i.name + ' ON ' + t.name + ' REBUILD;' + CHAR(13)");
            script.AppendLine("FROM sys.indexes i");
            script.AppendLine("INNER JOIN sys.tables t ON i.object_id = t.object_id");
            script.AppendLine("INNER JOIN sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') ps ON i.object_id = ps.object_id AND i.index_id = ps.index_id");
            script.AppendLine("WHERE ps.avg_fragmentation_in_percent > 30 AND i.index_id > 0;");
            script.AppendLine("EXEC sp_executesql @sql;");

            return Content(script.ToString(), "text/plain");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating optimization script");
            return Json(new { Success = false, Error = ex.Message });
        }
    }

    private async Task<List<object>> AnalyzeMissingIndexes()
    {
        var sql = @"
            SELECT 
                migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) AS improvement_measure,
                'CREATE INDEX [IX_' + OBJECT_NAME(mid.object_id) + '_' + REPLACE(REPLACE(REPLACE(ISNULL(mid.equality_columns,''), ', ', '_'), '[', ''), ']', '') + 
                CASE WHEN mid.inequality_columns IS NOT NULL THEN '_' + REPLACE(REPLACE(REPLACE(mid.inequality_columns, ', ', '_'), '[', ''), ']', '') ELSE '' END + ']' +
                ' ON ' + mid.statement + ' (' + ISNULL (mid.equality_columns,'') +
                CASE WHEN mid.equality_columns IS NOT NULL AND mid.inequality_columns IS NOT NULL THEN ',' ELSE '' END +
                CASE WHEN mid.inequality_columns IS NULL THEN '' ELSE mid.inequality_columns END + ')' +
                CASE WHEN mid.included_columns IS NULL THEN '' ELSE ' INCLUDE (' + mid.included_columns + ')' END AS create_index_statement,
                migs.user_seeks, migs.user_scans, migs.avg_total_user_cost, migs.avg_user_impact
            FROM sys.dm_db_missing_index_groups mig
            INNER JOIN sys.dm_db_missing_index_group_stats migs ON migs.group_handle = mig.index_group_handle
            INNER JOIN sys.dm_db_missing_index_details mid ON mig.index_handle = mid.index_handle
            WHERE migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) > 10
            ORDER BY improvement_measure DESC";

        try
        {
            var connection = _dbContext.Database.GetDbConnection();
            await connection.OpenAsync();
            
            using var command = connection.CreateCommand();
            command.CommandText = sql;
            
            var results = new List<object>();
            using var reader = await command.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                results.Add(new
                {
                    ImprovementMeasure = reader.GetDouble(0),
                    CreateIndexStatement = reader.GetString(1),
                    UserSeeks = reader.GetInt64(2),
                    UserScans = reader.GetInt64(3),
                    AvgTotalUserCost = reader.GetDouble(4),
                    AvgUserImpact = reader.GetDouble(5)
                });
            }
            
            return results;
        }
        catch
        {
            return new List<object> { new { Message = "Unable to analyze missing indexes - requires SQL Server" } };
        }
    }

    private async Task<List<object>> AnalyzeUnusedIndexes()
    {
        // Simplified version - in real implementation would check sys.dm_db_index_usage_stats
        return new List<object> { new { Message = "Unused index analysis requires extended monitoring" } };
    }

    private async Task<List<object>> AnalyzeFragmentedIndexes()
    {
        // Simplified version - in real implementation would check sys.dm_db_index_physical_stats
        return new List<object> { new { Message = "Fragmentation analysis requires SQL Server specific queries" } };
    }

    private async Task<List<object>> AnalyzeTableStatistics()
    {
        var tables = new[] { "Products", "ProductTranslations", "ProductCategories", "ProductCategoryTranslations" };
        var stats = new List<object>();

        foreach (var table in tables)
        {
            try
            {
                var count = await _dbContext.Database.ExecuteScalarAsync<int>($"SELECT COUNT(*) FROM [{table}]");
                stats.Add(new { TableName = table, RowCount = count });
            }
            catch (Exception ex)
            {
                stats.Add(new { TableName = table, Error = ex.Message });
            }
        }

        return stats;
    }

    private async Task<object> TestProductBySlugQuery()
    {
        var result = await _dbContext.ProductTranslations
            .Where(pt => pt.Slug == "lavanta-sabunu" && pt.LangPrefix == "tr" && !pt.IsDeleted)
            .Select(pt => new { pt.ProductId, pt.Caption })
            .AsNoTracking()
            .FirstOrDefaultAsync();
        
        return new { Found = result != null, Data = result };
    }

    private async Task<object> TestCategoryProductsQuery()
    {
        var count = await _dbContext.ProductCategoryRelations
            .Where(pcr => pcr.CategoryId == 1)
            .CountAsync();
        
        return new { ProductCount = count };
    }

    private async Task<object> TestNavigationQuery()
    {
        var count = await _dbContext.NavigationTranslations
            .Where(nt => nt.LangPrefix == "tr" && !nt.IsDeleted)
            .CountAsync();
        
        return new { NavigationCount = count };
    }

    private async Task<object> TestLanguagesQuery()
    {
        var count = await _dbContext.Languages
            .Where(l => l.IsActive && !l.IsDeleted)
            .CountAsync();
        
        return new { LanguageCount = count };
    }

    private async Task<object> TestSocialMediaQuery()
    {
        var result = await _dbContext.Contents
            .Where(c => c.Key == "SOCIAL_ACCOUNT_ADDRESS_LIST" && !c.IsDeleted)
            .Select(c => c.Value.Length)
            .FirstOrDefaultAsync();
        
        return new { ContentLength = result };
    }

    private List<string> GenerateRecommendations(List<object> missingIndexes, List<object> unusedIndexes, List<object> fragmentedIndexes)
    {
        var recommendations = new List<string>();

        if (missingIndexes.Any())
        {
            recommendations.Add($"Consider creating {missingIndexes.Count} missing indexes for better query performance");
        }

        recommendations.Add("Run the performance migration to add optimized indexes");
        recommendations.Add("Enable query store for better performance monitoring");
        recommendations.Add("Consider implementing read replicas for heavy read workloads");
        recommendations.Add("Monitor slow query log and optimize frequently used queries");

        return recommendations;
    }
}
