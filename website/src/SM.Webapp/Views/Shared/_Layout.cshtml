﻿@using SM.Webapp.Resources
@using SM.Service.Contents.DTOs
<!DOCTYPE html>
<!--[if lt IE 10]> <html  lang="@LocalizerRes.language" class="iex"> <![endif]-->
<!--[if (gt IE 10)|!(IE)]><!-->
<html lang="@LocalizerRes.language">
<!--<![endif]-->
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@ViewBag.PageTitle</title>
    <link rel="icon" type="image/png" href="/images/icons/favicon.png">
    <meta name="description" content="@Html.Raw(ViewBag.PageDescription)">
    <meta name="keywords" content="@Html.Raw(ViewBag.PageKeywords)">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@Html.Raw(ViewBag.OgTitle ?? ViewBag.PageTitle)">
    <meta property="og:description" content="@Html.Raw(ViewBag.OgDescription ?? ViewBag.PageDescription)">
    <meta property="og:image" content="@(ViewBag.OgImage ?? "https://www.sabunmutfagi.com/images/logos/sabun-mutfagi-og-image.jpg")">
    <meta property="og:image:alt" content="@Html.Raw(LocalizerRes.social_og_image_alt)">
    <meta property="og:url" content="@(ViewBag.OgUrl ?? ("https://www.sabunmutfagi.com" + Context.Request.Path))">
    <meta property="og:type" content="@(ViewBag.OgType ?? "website")">
    <meta property="og:site_name" content="@Html.Raw(LocalizerRes.social_og_site_name)">
    <meta property="og:locale" content="@(LocalizerRes.language == "tr" ? "tr_TR" : LocalizerRes.language == "en" ? "en_US" : "ru_RU")">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="@(ViewBag.TwitterCard ?? "summary_large_image")">
    <meta name="twitter:site" content="@Html.Raw(LocalizerRes.social_twitter_site)">
    <meta name="twitter:creator" content="@Html.Raw(LocalizerRes.social_twitter_site)">
    <meta name="twitter:title" content="@Html.Raw(ViewBag.TwitterTitle ?? ViewBag.PageTitle)">
    <meta name="twitter:description" content="@Html.Raw(ViewBag.TwitterDescription ?? ViewBag.PageDescription)">
    <meta name="twitter:image" content="@(ViewBag.TwitterImage ?? "https://www.sabunmutfagi.com/images/logos/sabun-mutfagi-twitter-card.jpg")">
    <meta name="twitter:image:alt" content="@Html.Raw(LocalizerRes.social_twitter_image_alt)">

    @if (ViewBag.RelCanonicals != null)
    {
        @foreach (LanguageDto relCanonical in ViewBag.RelCanonicals)
        {
            if (relCanonical.IsSelected)
            {
                <link rel="canonical" href="@("https://www.sabunmutfagi.com" + relCanonical.Url)">
            }
        }
    }

    <!-- Hreflang Tags for Multilingual SEO -->
    @if (ViewBag.HreflangUrls != null)
    {
        @foreach (LanguageDto hreflangUrl in ViewBag.HreflangUrls)
        {
            <link rel="alternate" hreflang="@hreflangUrl.HreflangCode" href="@hreflangUrl.FullUrl">
        }
        <!-- Default language fallback -->
        <link rel="alternate" hreflang="x-default" href="https://www.sabunmutfagi.com/">
    }
    <!-- Font Imports -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Domine:wght@400;500;700&family=Roboto:wght@400;500&family=Literata:opsz,wght@7..72,700&display=swap" rel="stylesheet">

    <!-- Core Style -->
    <link rel="stylesheet" href="/css/style-min.css">

    <!-- Font Icons -->
    <link rel="stylesheet" href="/css/font-icons-min.css">

    <!-- Niche Demos -->
    <link rel="stylesheet" href="/css/skincare-min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/custom-min.css">

    @await RenderSectionAsync("Styles", false)

    <!-- Structured Data -->
    @await RenderSectionAsync("StructuredData", false)

    <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-WP6P5VD083"></script>
        <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-WP6P5VD083');
        </script>

</head>
<body class="stretched">

<!-- Document Wrapper
============================================= -->
<div id="wrapper">

    @await Component.InvokeAsync("Header")

    @RenderBody()

    @await Component.InvokeAsync("Footer")
</div><!-- #wrapper end -->

<!-- Go To Top
============================================= -->
<div id="gotoTop" class="uil uil-angle-up rounded-circle" style="left: auto; right: 30px;"></div>

<!-- JavaScripts
============================================= -->
<script src="/js/plugins.min.js"></script>
<script src="/js/functions.bundle-min.js"></script>


@await RenderSectionAsync("Scripts", false)

</body>
</html>