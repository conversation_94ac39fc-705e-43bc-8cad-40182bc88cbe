using Microsoft.EntityFrameworkCore;
using SM.Service.Catalog.Contracts;
using SM.Service.Catalog.DTOs;
using SM.Service.Catalog.Exceptions;
using SM.Service.Contents.Contracts;
using SM.Service.Contents.DTOs;

namespace SM.Service.Catalog.Implementations;

/// <summary>
/// Highly optimized catalog service with single-query operations
/// </summary>
public class OptimizedCatalogService : SMServiceBase, ICatalogService
{
    private readonly IContentService _contentService;

    public OptimizedCatalogService(IServiceProvider serviceProvider, IContentService contentService) : base(serviceProvider)
    {
        _contentService = contentService;
    }

    public async Task<GetProductsResponse> GetProducts(GetProductsRequest request)
    {
        // Single optimized query with all required data
        var query = from pct in DbContext.ProductCategoryTranslations
                    join pc in DbContext.ProductCategories on pct.CategoryId equals pc.Id
                    join pcr in DbContext.ProductCategoryRelations on pc.Id equals pcr.CategoryId
                    join p in DbContext.Products on pcr.ProductId equals p.Id
                    join pt in DbContext.ProductTranslations on p.Id equals pt.ProductId
                    where pct.Slug == request.Slug 
                          && pct.LangPrefix == request.CurrentCultureInfo
                          && !pct.IsDeleted
                          && pc.IsActive && !pc.IsDeleted
                          && p.IsActive && !p.IsDeleted
                          && pt.LangPrefix == request.CurrentCultureInfo
                          && !pt.IsDeleted
                    select new
                    {
                        // Product data
                        ProductId = p.Id,
                        ProductCode = p.Code,
                        ProductSlug = pt.Slug,
                        ProductCaption = pt.Caption,
                        ProductTitle = pt.Title,
                        ProductDescription = pt.Description,
                        ProductSeoTitle = pt.SeoTitle,
                        ProductSeoDescription = pt.SeoDescription,
                        ProductSeoKeywords = pt.SeoKeywords,
                        
                        // Category data
                        CategoryId = pc.Id,
                        CategoryCaption = pct.Caption,
                        CategorySlug = pct.Slug,
                        CategoryBannerImagePath = pct.BannerImagePath,
                        CategoryDescription = pct.Description,
                        CategorySeoTitle = pct.SeoTitle,
                        CategorySeoDescription = pct.SeoDescription,
                        CategorySeoKeywords = pct.SeoKeywords
                    };

        var results = await query.AsNoTracking().ToListAsync();
        
        if (!results.Any())
        {
            throw new NotFoundCategoryException("Not Found Category");
        }

        // Get product images in a separate optimized query
        var productIds = results.Select(r => r.ProductId).Distinct().ToList();
        var productImages = await DbContext.ProductImages
            .Where(pi => productIds.Contains(pi.ProductId) && !pi.IsDeleted && pi.IsActive)
            .Select(pi => new { pi.ProductId, pi.Path, pi.IsMain, pi.IsList })
            .AsNoTracking()
            .ToListAsync();

        // Group images by product
        var imagesByProduct = productImages.GroupBy(img => img.ProductId)
            .ToDictionary(g => g.Key, g => g.ToList());

        // Build response
        var categoryData = results.First();
        var productDtos = results.GroupBy(r => r.ProductId)
            .Select(g =>
            {
                var product = g.First();
                var images = imagesByProduct.GetValueOrDefault(product.ProductId, new List<dynamic>());
                
                return new ProductDto
                {
                    Slug = product.ProductSlug,
                    Caption = product.ProductCaption,
                    Title = product.ProductTitle,
                    Description = product.ProductDescription,
                    SeoTitle = product.ProductSeoTitle,
                    SeoDescription = product.ProductSeoDescription,
                    SeoKeywords = product.ProductSeoKeywords,
                    MainImagePath = images.FirstOrDefault(img => img.IsMain)?.Path ?? images.FirstOrDefault()?.Path,
                    ListImagePath = images.FirstOrDefault(img => img.IsList)?.Path ?? images.FirstOrDefault()?.Path,
                    ImagePaths = images.Select(img => img.Path).ToList()
                };
            }).ToList();

        // Get other data in parallel
        var productCategoriesTask = GetProductCategory(request.CurrentCultureInfo);
        var socialMediaTask = _contentService.GetSocialMediaAccountAddresses();
        var languagesTask = PrepareLanguageUrlsOptimized(request.CurrentCultureInfo, categoryData.CategoryId);

        await Task.WhenAll(productCategoriesTask, socialMediaTask, languagesTask);

        return new GetProductsResponse
        {
            Products = productDtos,
            ProductCategories = await productCategoriesTask,
            PageContent = new ProductsPageContent
            {
                Title = categoryData.CategoryCaption,
                Description = categoryData.CategoryDescription,
                ImagePath = categoryData.CategoryBannerImagePath,
                SeoTitle = categoryData.CategorySeoTitle,
                SeoDescription = categoryData.CategorySeoDescription,
                SeoKeywords = categoryData.CategorySeoKeywords,
            },
            SocialMediaAccountAddresses = await socialMediaTask,
            Languages = await languagesTask
        };
    }

    public async Task<GetProductDetailResponse> GetProductDetail(GetProductDetailRequest request)
    {
        // Single optimized query for product detail
        var productData = await (from pt in DbContext.ProductTranslations
                                join p in DbContext.Products on pt.ProductId equals p.Id
                                where pt.Slug == request.Slug 
                                      && pt.LangPrefix == request.CurrentCultureInfo
                                      && !pt.IsDeleted
                                      && p.IsActive && !p.IsDeleted
                                select new
                                {
                                    ProductId = p.Id,
                                    ProductCode = p.Code,
                                    pt.Caption,
                                    pt.Title,
                                    pt.Description,
                                    pt.Ingredients,
                                    pt.HowToApply,
                                    pt.SkinMoisture,
                                    pt.Warnings,
                                    pt.Dimensions,
                                    pt.SeoTitle,
                                    pt.SeoDescription,
                                    pt.SeoKeywords,
                                    pt.Slug
                                }).AsNoTracking().FirstOrDefaultAsync();

        if (productData == null)
        {
            throw new NotFoundProductException("Not Found Product");
        }

        // Get product images and translations in parallel
        var imagesTask = DbContext.ProductImages
            .Where(pi => pi.ProductId == productData.ProductId && !pi.IsDeleted)
            .Select(pi => new { pi.Path, pi.IsMain, pi.IsList })
            .AsNoTracking()
            .ToListAsync();

        var translationsTask = DbContext.ProductTranslations
            .Where(pt => pt.ProductId == productData.ProductId && !pt.IsDeleted)
            .Select(pt => new { pt.LangPrefix, pt.Slug })
            .AsNoTracking()
            .ToListAsync();

        var socialMediaTask = _contentService.GetSocialMediaAccountAddresses();

        await Task.WhenAll(imagesTask, translationsTask, socialMediaTask);

        var images = await imagesTask;
        var translations = await translationsTask;

        return new GetProductDetailResponse
        {
            Product = new ProductDetailDto
            {
                Caption = productData.Caption,
                Title = productData.Title,
                Description = productData.Description,
                Ingredients = productData.Ingredients,
                HowToApply = productData.HowToApply,
                SkinMoisture = productData.SkinMoisture,
                Warnings = productData.Warnings,
                Dimensions = productData.Dimensions,
                SeoTitle = productData.SeoTitle,
                SeoDescription = productData.SeoDescription,
                SeoKeywords = productData.SeoKeywords,
                MainImagePath = images.FirstOrDefault(img => img.IsMain)?.Path ?? images.FirstOrDefault()?.Path,
                ListImagePath = images.FirstOrDefault(img => img.IsList)?.Path ?? images.FirstOrDefault()?.Path,
                ImagePaths = images.Select(img => img.Path).ToList()
            },
            SocialMediaAccountAddresses = await socialMediaTask,
            Languages = await PrepareLanguageUrlsFromTranslations(request.CurrentCultureInfo, translations.ToList())
        };
    }

    public async Task<IEnumerable<ProductCategoryDto>> GetProductCategory(string currentCultureInfo)
    {
        return await DbContext.NavigationTranslations
            .Where(nt => nt.Navigation.Type == NavigationType.Category 
                        && nt.LangPrefix == currentCultureInfo
                        && nt.Navigation.IsActive 
                        && !nt.Navigation.IsDeleted
                        && !nt.IsDeleted)
            .Select(nt => new ProductCategoryDto
            {
                Caption = nt.Caption,
                Slug = Utils.PrepareUrl(NavigationType.Category, currentCultureInfo, nt.Link),
                SortPriority = nt.Navigation.SortPriority
            })
            .OrderBy(pc => pc.SortPriority)
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<string> GetCategorySlugByProductSlugAsync(string productSlug, string langPrefix)
    {
        var categorySlug = await (from pt in DbContext.ProductTranslations
                                 join pcr in DbContext.ProductCategoryRelations on pt.ProductId equals pcr.ProductId
                                 join pct in DbContext.ProductCategoryTranslations on pcr.CategoryId equals pct.CategoryId
                                 where pt.Slug == productSlug 
                                       && pt.LangPrefix == langPrefix
                                       && pct.LangPrefix == langPrefix
                                       && !pt.IsDeleted
                                       && !pct.IsDeleted
                                 select pct.Slug)
                                .AsNoTracking()
                                .FirstOrDefaultAsync();

        return categorySlug ?? GetFallbackCategorySlug(langPrefix);
    }

    private async Task<IEnumerable<LanguageDto>> PrepareLanguageUrlsOptimized(string currentCultureInfo, int categoryId)
    {
        var languages = await _contentService.GetLanguages(currentCultureInfo);
        var categoryTranslations = await DbContext.ProductCategoryTranslations
            .Where(pct => pct.CategoryId == categoryId && !pct.IsDeleted)
            .Select(pct => new { pct.LangPrefix, pct.Slug })
            .AsNoTracking()
            .ToListAsync();

        var translationDict = categoryTranslations.ToDictionary(ct => ct.LangPrefix, ct => ct.Slug);

        return languages.Select(lang => new LanguageDto
        {
            Caption = lang.Caption,
            Prefix = lang.Prefix,
            Url = translationDict.ContainsKey(lang.Prefix) 
                ? $"/{lang.Prefix}/{translationDict[lang.Prefix]}" 
                : lang.Url
        });
    }

    private async Task<IEnumerable<LanguageDto>> PrepareLanguageUrlsFromTranslations(string currentCultureInfo, List<dynamic> translations)
    {
        var languages = await _contentService.GetLanguages(currentCultureInfo);
        var translationDict = translations.ToDictionary(t => t.LangPrefix, t => t.Slug);

        return languages.Select(lang => new LanguageDto
        {
            Caption = lang.Caption,
            Prefix = lang.Prefix,
            Url = translationDict.ContainsKey(lang.Prefix) 
                ? $"/{lang.Prefix}/product/{translationDict[lang.Prefix]}" 
                : lang.Url
        });
    }

    private string GetFallbackCategorySlug(string langPrefix)
    {
        return langPrefix switch
        {
            "tr" => "sabunlar",
            "en" => "soaps",
            "ru" => "мыло",
            _ => "sabunlar"
        };
    }
}
