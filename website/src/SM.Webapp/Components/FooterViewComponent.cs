using Microsoft.AspNetCore.Mvc;
using SM.Service.Contents.Contracts;
using SM.Webapp.Core;

namespace SM.WebApp.Components;

public class FooterViewComponent : ViewComponent
{
    public FooterViewComponent(IContentService contentService)
    {
        _contentService = contentService;
    }

    private IContentService _contentService { get; }

    public async Task<IViewComponentResult> InvokeAsync()
    {
        var contactInfo = await _contentService.GetContactInfo(RouteData.GetLanguage());
        return View(contactInfo);
    }
}