using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SM.Service.Catalog.Contracts;
using SM.Service.Catalog.Implementations;
using SM.Service.Contents.Contracts;
using SM.Service.Contents.Implementations;
using SM.Service.Message.Implementations;

namespace SM.Service;

public static class Module
{
    public static IServiceCollection AddServiceModule(this IServiceCollection collection, IConfiguration configuration)
    {
        // Register base services
        collection.AddScoped<ContentService>();
        collection.AddScoped<CatalogService>();
        collection.AddScoped<OptimizedContentService>();
        collection.AddScoped<OptimizedCatalogService>();
        collection.AddScoped<IMessageService, MessageService>();

        // Check performance mode
        var enableCaching = configuration.GetValue<bool>("Cache:Enabled", true);
        var isLowMemoryMode = configuration.GetValue<bool>("Cache:LowMemoryMode", false);
        var useOptimizedServices = configuration.GetValue<bool>("Performance:UseOptimizedServices", true);

        if (isLowMemoryMode)
        {
            // Ultra low memory - no caching, optimized queries
            collection.AddScoped<IContentService, OptimizedContentService>();
            collection.AddScoped<ICatalogService, OptimizedCatalogService>();
        }
        else if (!enableCaching)
        {
            // No caching but standard services
            collection.AddScoped<IContentService, LowMemoryContentService>();
            collection.AddScoped<ICatalogService, CatalogService>();
        }
        else if (useOptimizedServices)
        {
            // Cached + optimized services (best performance)
            collection.AddScoped<IContentService>(provider =>
                new CachedContentService(
                    provider.GetRequiredService<OptimizedContentService>(),
                    provider.GetRequiredService<IMemoryCache>(),
                    provider.GetRequiredService<ILogger<CachedContentService>>()));

            collection.AddScoped<ICatalogService>(provider =>
                new CachedCatalogService(
                    provider.GetRequiredService<OptimizedCatalogService>(),
                    provider.GetRequiredService<IMemoryCache>(),
                    provider.GetRequiredService<ILogger<CachedCatalogService>>()));
        }
        else
        {
            // Standard cached services
            collection.AddScoped<IContentService, CachedContentService>();
            collection.AddScoped<ICatalogService, CachedCatalogService>();
        }

        return collection;
    }
}