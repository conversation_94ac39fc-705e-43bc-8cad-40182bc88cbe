{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Database": {"Provider": "SqlServer", "ConnectionString": "data source=***************\\MSSQLSERVER2022;initial catalog=sabunmutfagi_prod;user=sabunmutfagi_website;password=***************;MultipleActiveResultSets=True;TrustServerCertificate=True;"}, "ApiSettings": {"SitemapApiKey": "DEV_SitemapKey_2024_LocalTest"}, "Performance": {"SlowRequestThresholdMs": 500, "CacheDefaultDurationMinutes": 5}, "Cache": {"MaxEntries": 100, "CompactionPercentage": 0.25, "ScanFrequencyMinutes": 5, "DefaultDurationMinutes": 5, "LongDurationMinutes": 15, "ShortDurationMinutes": 2}}